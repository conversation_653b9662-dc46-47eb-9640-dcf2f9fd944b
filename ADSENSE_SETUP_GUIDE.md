# Google AdSense 广告单元配置指南

## 概述

本指南将帮助您在 AFT Calculator 项目中正确配置 Google AdSense 广告单元。我们已经在项目中集成了广告组件，现在您需要创建相应的广告单元并替换占位符 ID。

## 已集成的广告位置

### 1. 主页 (/)

#### 顶部横幅广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Mobile Banner (300x50)
- **位置**: Hero 区域下方
- **占位符 ID**: 
  - `YOUR_LEADERBOARD_AD_UNIT_ID`
  - `YOUR_MOBILE_BANNER_AD_UNIT_ID`

#### 中间内容广告
- **桌面端**: Large Rectangle (336x280)
- **移动端**: Medium Rectangle (300x250)
- **位置**: 计算器和功能介绍之间
- **占位符 ID**: 
  - `YOUR_LARGE_RECTANGLE_AD_UNIT_ID`
  - `YOUR_MEDIUM_RECTANGLE_AD_UNIT_ID`

#### 底部横幅广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Mobile Banner (300x50)
- **位置**: Call to Action 区域下方
- **占位符 ID**: 
  - `YOUR_BOTTOM_LEADERBOARD_AD_UNIT_ID`
  - `YOUR_BOTTOM_MOBILE_BANNER_AD_UNIT_ID`

### 2. AFT 计算器页面

#### 计算器侧边栏广告
- **尺寸**: Large Rectangle (336x280)
- **位置**: 计算器右侧（仅桌面端显示）
- **占位符 ID**: `YOUR_CALCULATOR_SIDEBAR_AD_UNIT_ID`

#### 计算器底部广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Medium Rectangle (300x250)
- **位置**: 计算器组件下方
- **占位符 ID**: 
  - `YOUR_CALCULATOR_BOTTOM_AD_UNIT_ID`
  - `YOUR_CALCULATOR_MOBILE_BOTTOM_AD_UNIT_ID`

### 3. AFT 标准页面 (/aft-standards)

#### 顶部横幅广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Mobile Banner (300x50)
- **占位符 ID**: 
  - `YOUR_STANDARDS_TOP_AD_UNIT_ID`
  - `YOUR_STANDARDS_MOBILE_TOP_AD_UNIT_ID`

#### 中间内容广告
- **尺寸**: Medium Rectangle (300x250)
- **位置**: 计算器和信息区域之间
- **占位符 ID**: `YOUR_STANDARDS_MID_AD_UNIT_ID`

#### 底部横幅广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Mobile Banner (300x50)
- **占位符 ID**: 
  - `YOUR_STANDARDS_BOTTOM_AD_UNIT_ID`
  - `YOUR_STANDARDS_MOBILE_BOTTOM_AD_UNIT_ID`

### 4. AFT 评分表页面 (/aft-score-chart)

#### 顶部横幅广告
- **桌面端**: Leaderboard (728x90)
- **移动端**: Mobile Banner (300x50)
- **占位符 ID**: 
  - `YOUR_SCORE_CHART_TOP_AD_UNIT_ID`
  - `YOUR_SCORE_CHART_MOBILE_TOP_AD_UNIT_ID`

#### 中间内容广告
- **尺寸**: Medium Rectangle (300x250)
- **位置**: 评分表和描述区域之间
- **占位符 ID**: `YOUR_SCORE_CHART_MID_AD_UNIT_ID`

## Google AdSense 广告单元创建指南

### 步骤 1: 登录 AdSense 控制台
1. 访问 [Google AdSense](https://www.google.com/adsense/)
2. 使用您的 Google 账户登录

### 步骤 2: 创建广告单元
1. 在左侧菜单中选择"广告"
2. 点击"按广告单元"
3. 点击"创建新广告单元"

### 步骤 3: 配置广告单元

#### 对于 Leaderboard (728x90) 广告:
- **广告单元名称**: `AFT Calculator - Leaderboard - [位置描述]`
- **广告类型**: 展示广告
- **广告尺寸**: 固定尺寸 → 728x90 (横幅广告)
- **广告格式**: 横向

#### 对于 Large Rectangle (336x280) 广告:
- **广告单元名称**: `AFT Calculator - Large Rectangle - [位置描述]`
- **广告类型**: 展示广告
- **广告尺寸**: 固定尺寸 → 336x280 (大矩形)
- **广告格式**: 矩形

#### 对于 Medium Rectangle (300x250) 广告:
- **广告单元名称**: `AFT Calculator - Medium Rectangle - [位置描述]`
- **广告类型**: 展示广告
- **广告尺寸**: 固定尺寸 → 300x250 (中矩形)
- **广告格式**: 矩形

#### 对于 Mobile Banner (300x50) 广告:
- **广告单元名称**: `AFT Calculator - Mobile Banner - [位置描述]`
- **广告类型**: 展示广告
- **广告尺寸**: 固定尺寸 → 300x50 (移动横幅)
- **广告格式**: 横向

### 步骤 4: 获取广告单元 ID
1. 创建广告单元后，复制生成的广告单元 ID
2. 广告单元 ID 格式通常为: `**********`

## 配置文件更新

### 环境变量配置
确保在 `.env.local` 文件中设置了正确的 AdSense 账户 ID:

```env
NEXT_PUBLIC_ADSENSE_ACCOUNT=ca-pub-YOUR_PUBLISHER_ID
NEXT_PUBLIC_GOOGLE_ADSENSE_CODE=ca-pub-YOUR_PUBLISHER_ID
```

### 替换占位符 ID

您需要在以下文件中替换占位符 ID:

1. **src/app/page.tsx** - 主页广告
2. **src/components/calculator/AFTCalculator.tsx** - 计算器页面广告
3. **src/app/aft-standards/page.tsx** - 标准页面广告
4. **src/app/aft-score-chart/page.tsx** - 评分表页面广告

### 替换示例

将占位符:
```tsx
adUnitId="YOUR_LEADERBOARD_AD_UNIT_ID"
```

替换为实际的广告单元 ID:
```tsx
adUnitId="**********"
```

## 广告单元尺寸建议

### 桌面端推荐尺寸:
- **顶部/底部横幅**: 728x90 (Leaderboard)
- **侧边栏**: 336x280 (Large Rectangle)
- **内容中间**: 300x250 (Medium Rectangle)

### 移动端推荐尺寸:
- **顶部/底部横幅**: 300x50 (Mobile Banner)
- **内容中间**: 300x250 (Medium Rectangle)

### 平板端:
- 自动使用桌面端或移动端尺寸，根据屏幕宽度判断

## 最佳实践

1. **广告密度**: 确保广告不会过于密集，影响用户体验
2. **加载性能**: 广告组件已优化为异步加载，不会阻塞页面渲染
3. **响应式设计**: 使用我们提供的响应式广告组件确保在所有设备上正确显示
4. **AdSense 政策**: 确保遵守 Google AdSense 的内容政策和放置政策

## 测试和验证

1. 替换所有占位符 ID 后，在本地环境测试
2. 部署到生产环境
3. 使用 Google AdSense 控制台监控广告表现
4. 检查广告是否在不同设备上正确显示

## 故障排除

### 广告不显示:
1. 检查广告单元 ID 是否正确
2. 确认 AdSense 账户状态正常
3. 检查浏览器是否启用了广告拦截器
4. 查看浏览器控制台是否有错误信息

### 响应式问题:
1. 检查 CSS 类名是否正确应用
2. 测试不同屏幕尺寸下的显示效果
3. 确认移动端和桌面端使用了不同的广告单元 ID

## 快速参考 - 所有广告单元 ID 占位符

### 需要替换的占位符列表:

| 页面 | 位置 | 尺寸 | 占位符 ID |
|------|------|------|-----------|
| 主页 | 顶部横幅 (桌面) | 728x90 | `YOUR_LEADERBOARD_AD_UNIT_ID` |
| 主页 | 顶部横幅 (移动) | 300x50 | `YOUR_MOBILE_BANNER_AD_UNIT_ID` |
| 主页 | 中间内容 (桌面) | 336x280 | `YOUR_LARGE_RECTANGLE_AD_UNIT_ID` |
| 主页 | 中间内容 (移动) | 300x250 | `YOUR_MEDIUM_RECTANGLE_AD_UNIT_ID` |
| 主页 | 底部横幅 (桌面) | 728x90 | `YOUR_BOTTOM_LEADERBOARD_AD_UNIT_ID` |
| 主页 | 底部横幅 (移动) | 300x50 | `YOUR_BOTTOM_MOBILE_BANNER_AD_UNIT_ID` |
| 计算器 | 侧边栏 | 336x280 | `YOUR_CALCULATOR_SIDEBAR_AD_UNIT_ID` |
| 计算器 | 底部 (桌面) | 728x90 | `YOUR_CALCULATOR_BOTTOM_AD_UNIT_ID` |
| 计算器 | 底部 (移动) | 300x250 | `YOUR_CALCULATOR_MOBILE_BOTTOM_AD_UNIT_ID` |
| 标准页面 | 顶部 (桌面) | 728x90 | `YOUR_STANDARDS_TOP_AD_UNIT_ID` |
| 标准页面 | 顶部 (移动) | 300x50 | `YOUR_STANDARDS_MOBILE_TOP_AD_UNIT_ID` |
| 标准页面 | 中间 | 300x250 | `YOUR_STANDARDS_MID_AD_UNIT_ID` |
| 标准页面 | 底部 (桌面) | 728x90 | `YOUR_STANDARDS_BOTTOM_AD_UNIT_ID` |
| 标准页面 | 底部 (移动) | 300x50 | `YOUR_STANDARDS_MOBILE_BOTTOM_AD_UNIT_ID` |
| 评分表页面 | 顶部 (桌面) | 728x90 | `YOUR_SCORE_CHART_TOP_AD_UNIT_ID` |
| 评分表页面 | 顶部 (移动) | 300x50 | `YOUR_SCORE_CHART_MOBILE_TOP_AD_UNIT_ID` |
| 评分表页面 | 中间 | 300x250 | `YOUR_SCORE_CHART_MID_AD_UNIT_ID` |

### 建议的广告单元命名规范:

```
AFT Calculator - [尺寸] - [页面] - [位置]

示例:
- AFT Calculator - Leaderboard - Home - Top
- AFT Calculator - Mobile Banner - Home - Top
- AFT Calculator - Large Rectangle - Home - Mid
- AFT Calculator - Medium Rectangle - Calculator - Bottom
- AFT Calculator - Large Rectangle - Calculator - Sidebar
```

## 联系支持

如果在配置过程中遇到问题，请参考:
- [Google AdSense 帮助中心](https://support.google.com/adsense/)
- [AdSense 政策中心](https://support.google.com/adsense/answer/48182)
