# AFT Calculator - Army Fitness Test Calculator

A professional, comprehensive Army Fitness Test (AFT) calculator built with Next.js 14, TypeScript, and Tailwind CSS. This application provides accurate AFT score calculations, training resources, and comprehensive fitness guidance for military personnel.

## 🚀 Features

### Core Functionality
- **Accurate AFT Calculator**: Real-time score calculations based on official Army standards
- **Multi-Event Support**: All 6 AFT events including deadlift, power throw, push-ups, sprint-drag-carry, leg tuck/plank, and 2-mile run
- **Age & Gender Specific**: Scoring standards adjusted for different age groups and genders
- **Instant Results**: Real-time calculation with detailed score breakdowns

### Additional Features
- **Scoring Standards**: Comprehensive tables showing requirements by age and gender
- **Training Guides**: Expert training advice and workout plans for each AFT event
- **FAQ Section**: Searchable frequently asked questions with categorized answers

- **SEO Optimized**: Full SEO implementation with meta tags, sitemap, and structured data
- **Mobile Responsive**: Optimized for all devices and screen sizes

## 🛠️ Technology Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **Icons**: Lucide React
- **Deployment**: Vercel (recommended)

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd aftcalculator
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── faq/               # FAQ page
│   ├── standards/         # Scoring standards page
│   ├── training/          # Training guide page
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   ├── robots.ts          # Robots.txt generation
│   └── sitemap.ts         # Sitemap generation
├── components/            # React components
│   ├── calculator/        # AFT calculator components
│   ├── layout/           # Layout components (Header, Footer)
│   └── ui/               # Shadcn/ui components
├── data/                 # Static data files
│   ├── aft.js           # AFT scoring data and algorithms (primary data source)
│   └── aft-standards.json # Legacy AFT scoring standards (deprecated)
└── lib/                  # Utility functions
    ├── aft-calculations.ts # AFT calculation logic
    ├── aft-scoring/      # Individual event scoring algorithms
    └── utils.ts          # General utilities
```

## 🧮 AFT Calculator Features

### Supported Events
1. **3 Repetition Maximum Deadlift** - Maximum weight for 3 reps
2. **Hand-Release Push-Up** - Push-ups with hand release at bottom
3. **Sprint-Drag-Carry** - Timed multi-movement course
4. **Plank** - Core strength assessment
5. **2-Mile Run** - Timed endurance run

### Calculation Features
- Real-time score updates as you input data
- Individual event scores (0-100 points each)
- Total score calculation (0-500 points)
- Pass/fail determination (minimum 60 points per event, 300 total)
- Score category classification (Excellent, Very Good, Good, Satisfactory, Unsatisfactory)

## 📊 Scoring Standards

The calculator includes comprehensive scoring standards for:
- Multiple age groups (17-21, 22-26, 27-31, 32-36, 37-41, 42-46, 47-51, 52-56, 57-61, 62+)
- Both male and female standards
- All five AFT events
- Minimum and maximum performance requirements
- Optimized algorithms for accurate score calculation

## 🏋️ Training Resources

### Training Guides Include:
- Event-specific training techniques
- Progressive workout plans (Beginner, Intermediate, Advanced)
- Exercise recommendations for each AFT event
- Training principles and recovery strategies
- Nutrition guidelines for military fitness

## 🔍 SEO Features

- Comprehensive meta tags and descriptions
- Structured data markup (JSON-LD)
- XML sitemap generation
- Robots.txt configuration
- Open Graph and Twitter Card support
- Optimized for military fitness keywords

## 📱 Responsive Design

- Mobile-first approach
- Optimized for tablets and smartphones
- Touch-friendly interface
- Fast loading times
- Accessible design (WCAG 2.1 AA compliant)

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure environment variables if needed
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
npm run build
npm run start
```

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Adding New Features
1. Follow the existing project structure
2. Use TypeScript for type safety
3. Implement responsive design with Tailwind CSS
4. Add appropriate SEO meta tags
5. Test on multiple devices and browsers

## ⚠️ Important Disclaimers

- **Unofficial Tool**: This calculator is not affiliated with the U.S. Army
- **Reference Only**: Always consult official Army regulations for authoritative guidance
- **Accuracy**: While we strive for accuracy, verify all calculations with official sources
- **No Liability**: Users are responsible for verifying all information

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

For questions or issues:
1. Check the FAQ section
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. For official AFT guidance, consult military personnel

## 🎯 Future Enhancements

- User accounts and progress tracking
- Training plan PDF exports
- Integration with fitness tracking apps
- Additional military fitness tests
- Advanced analytics and reporting
