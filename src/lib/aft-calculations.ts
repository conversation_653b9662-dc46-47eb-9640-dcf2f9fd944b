import { calculateMDLScore } from './aft-scoring/mdl-scoring';
import { calculateHRPScore } from './aft-scoring/hrp-scoring';
import { calculateSDCScore } from './aft-scoring/sdc-scoring';
import { calculatePLKScore } from './aft-scoring/plk-scoring';
import { calculate2MRScore } from './aft-scoring/tmr-scoring';
import { getAgeGroup } from '@/data/aft';

export interface AFTInput {
  age: number;
  gender: 'male' | 'female';
  deadlift: number; // lbs
  pushUp: number; // reps
  sprintDragCarry: number; // seconds
  plank: number; // seconds
  twoMileRun: number; // seconds
}

export interface AFTResult {
  totalScore: number;
  passed: boolean;
  scores: {
    deadlift: number;
    pushUp: number;
    sprintDragCarry: number;
    plank: number;
    twoMileRun: number;
  };
  minimumPassing: boolean;
}





export function calculateAFTScore(input: AFTInput): AFTResult {
  // 验证输入参数
  if (!input || typeof input.age !== 'number' || input.age < 17 || input.age > 100) {
    throw new Error(`Invalid age: ${input.age}. Age must be between 17 and 100.`);
  }

  if (!input.gender || !['male', 'female'].includes(input.gender)) {
    throw new Error(`Invalid gender: ${input.gender}. Gender must be 'male' or 'female'.`);
  }

  // 验证年龄组是否有数据支持
  const ageGroup = getAgeGroup(input.age);
  if (!ageGroup) {
    throw new Error(`No standards found for age ${input.age}`);
  }

  // Calculate individual event scores using algorithms from aft.js
  const deadliftScore = calculateMDLScore(input.deadlift, input.age, input.gender);
  const pushUpScore = calculateHRPScore(input.pushUp, input.age, input.gender);
  const sprintDragCarryScore = calculateSDCScore(input.sprintDragCarry, input.age, input.gender);

  // Plank score
  const plankScore = calculatePLKScore(input.plank, input.age, input.gender);

  const twoMileRunScore = calculate2MRScore(input.twoMileRun, input.age, input.gender);

  // Calculate total score (5 events: MDL, HRP, SDC, PLK, 2MR)
  const totalScore = deadliftScore + pushUpScore + sprintDragCarryScore + plankScore + twoMileRunScore;

  // Check if passed (minimum 60 points per event and 300 total for 5 events)
  const minimumEventScore = 60;
  const minimumTotalScore = 300;

  const allEventsPass = [
    deadliftScore,
    pushUpScore,
    sprintDragCarryScore,
    plankScore,
    twoMileRunScore
  ].every(score => score >= minimumEventScore);

  const passed = allEventsPass && totalScore >= minimumTotalScore;

  return {
    totalScore,
    passed,
    scores: {
      deadlift: deadliftScore,
      pushUp: pushUpScore,
      sprintDragCarry: sprintDragCarryScore,
      plank: plankScore,
      twoMileRun: twoMileRunScore,
    },
    minimumPassing: allEventsPass && totalScore >= minimumTotalScore,
  };
}

export function getScoreCategory(score: number): {
  category: string;
  color: string;
} {
  if (score >= 90) return { category: 'Excellent', color: 'text-green-600' };
  if (score >= 80) return { category: 'Very Good', color: 'text-blue-600' };
  if (score >= 70) return { category: 'Good', color: 'text-yellow-600' };
  if (score >= 60) return { category: 'Satisfactory', color: 'text-orange-600' };
  return { category: 'Unsatisfactory', color: 'text-red-600' };
}

export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function parseTimeToSeconds(timeString: string): number {
  const parts = timeString.split(':');
  if (parts.length === 2) {
    const minutes = parseInt(parts[0]);
    const seconds = parseInt(parts[1]);
    return minutes * 60 + seconds;
  }
  return parseInt(timeString) || 0;
}
