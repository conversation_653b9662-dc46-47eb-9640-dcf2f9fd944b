/**
 * AFT Max Deadlift (MDL) Scoring Algorithm
 * Based on official Army Fitness Test scoring tables
 * Effective: 1 June 2025
 */

import { calculateMDLScore as calculateMDLScoreFromAft, getMDLMinimumRequirements } from '@/data/aft.js';

export interface MDLScoreData {
  weight: number; // Weight lifted in pounds (LBS)
  points: number; // Points earned (0-100)
}

/**
 * Calculate MDL score based on weight lifted, age, and gender
 * @param weight - Weight lifted in pounds (LBS)
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Points earned (0-100)
 */
export function calculateMDLScore(weight: number, age: number, gender: 'male' | 'female'): number {
  // Input validation
  if (weight < 0 || age < 17 || age > 100) {
    return 0;
  }

  // Use the algorithm from aft.js for consistency
  return calculateMDLScoreFromAft(weight, age, gender);
}

/**
 * Get the minimum weight required for a specific score
 * @param targetScore - Target score (0-100)
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Minimum weight required in pounds
 */
export function getMinimumWeightForScore(targetScore: number, age: number, gender: 'male' | 'female'): number {
  if (targetScore < 0 || targetScore > 100) {
    return 0;
  }

  // For now, return a reasonable estimate based on minimum requirements
  // This function would need access to the full scoring table to be accurate
  const requirements = getMDLMinimumRequirements(age, gender);

  if (!requirements) {
    return 0;
  }

  // Return minimum value for 60+ score, or estimate based on target
  if (targetScore >= 60) {
    return requirements.minValue;
  }

  return 0; // Below passing score
}

/**
 * Get performance category based on score
 * @param score - Score (0-100)
 * @returns Performance category and color
 */
export function getMDLPerformanceCategory(score: number): {
  category: string;
  color: string;
  description: string;
} {
  if (score >= 90) {
    return {
      category: 'Excellent',
      color: 'text-green-600',
      description: 'Outstanding performance'
    };
  }
  if (score >= 80) {
    return {
      category: 'Very Good',
      color: 'text-blue-600',
      description: 'Above average performance'
    };
  }
  if (score >= 70) {
    return {
      category: 'Good',
      color: 'text-yellow-600',
      description: 'Good performance'
    };
  }
  if (score >= 60) {
    return {
      category: 'Satisfactory',
      color: 'text-orange-600',
      description: 'Meets minimum standards'
    };
  }
  return {
    category: 'Unsatisfactory',
    color: 'text-red-600',
    description: 'Below minimum standards'
  };
}

/**
 * Format weight for display
 * @param weight - Weight in pounds
 * @returns Formatted string
 */
export function formatMDLWeight(weight: number): string {
  return `${weight} lbs`;
}

/**
 * Validate MDL input
 * @param weight - Weight in pounds
 * @returns True if valid, false otherwise
 */
export function validateMDLInput(weight: number): boolean {
  return weight >= 0 && weight <= 500 && Number.isInteger(weight);
}