/**
 * AFT Hand-Release Push-Up (HRP) Scoring Algorithm
 * Based on official Army Fitness Test scoring tables
 * Effective: 1 June 2025
 */

import { calculateHRPScore as calculateHRPScoreFromAft, getHRPMinimumRequirements } from '@/data/aft.js';

export interface HRPScoreData {
  reps: number; // Number of repetitions
  points: number; // Points earned (0-100)
}

/**
 * Calculate HRP score based on repetitions, age, and gender
 * @param reps - Number of repetitions performed
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Points earned (0-100)
 */
export function calculateHRPScore(reps: number, age: number, gender: 'male' | 'female'): number {
  // Input validation
  if (reps < 0 || age < 17 || age > 100) {
    return 0;
  }

  // Use the algorithm from aft.js for consistency
  return calculateHRPScoreFromAft(reps, age, gender);
}

/**
 * Get the minimum repetitions required for a specific score
 * @param targetScore - Target score (0-100)
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Minimum repetitions required
 */
export function getMinimumRepsForScore(targetScore: number, age: number, gender: 'male' | 'female'): number {
  if (targetScore < 0 || targetScore > 100) {
    return 0;
  }

  // For now, return a reasonable estimate based on minimum requirements
  // This function would need access to the full scoring table to be accurate
  const requirements = getHRPMinimumRequirements(age, gender);

  if (!requirements) {
    return 0;
  }

  // Return minimum value for 60+ score, or estimate based on target
  if (targetScore >= 60) {
    return requirements.minValue;
  }

  return 0; // Below passing score
}

/**
 * Get performance category based on score
 * @param score - Score (0-100)
 * @returns Performance category and color
 */
export function getHRPPerformanceCategory(score: number): {
  category: string;
  color: string;
  description: string;
} {
  if (score >= 90) {
    return {
      category: 'Excellent',
      color: 'text-green-600',
      description: 'Outstanding performance'
    };
  }
  if (score >= 80) {
    return {
      category: 'Very Good',
      color: 'text-blue-600',
      description: 'Above average performance'
    };
  }
  if (score >= 70) {
    return {
      category: 'Good',
      color: 'text-yellow-600',
      description: 'Good performance'
    };
  }
  if (score >= 60) {
    return {
      category: 'Satisfactory',
      color: 'text-orange-600',
      description: 'Meets minimum standards'
    };
  }
  return {
    category: 'Unsatisfactory',
    color: 'text-red-600',
    description: 'Below minimum standards'
  };
}

/**
 * Format repetitions for display
 * @param reps - Number of repetitions
 * @returns Formatted string
 */
export function formatHRPReps(reps: number): string {
  return `${reps} reps`;
}

/**
 * Validate HRP input
 * @param reps - Number of repetitions
 * @returns True if valid, false otherwise
 */
export function validateHRPInput(reps: number): boolean {
  return reps >= 0 && reps <= 100 && Number.isInteger(reps);
}