import { getAgeGroup, timeToSeconds, calculatePLKScore as calculatePLKScoreFromAft, plk } from '@/data/aft.js';

/**
 * Calculate PLK (Plank) score based on time, age, and gender
 * @param timeValue - Time in seconds or time string (e.g., "2:30")
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Points earned (0-100)
 */
export function calculatePLKScore(timeValue: number | string, age: number, gender: 'male' | 'female'): number {
  // Input validation
  if (age < 17 || age > 100) {
    return 0;
  }

  // Convert time to seconds if it's a string
  const seconds = typeof timeValue === 'string' ? timeToSeconds(timeValue) : timeValue;
  if (seconds <= 0) {
    return 0;
  }

  // Use the algorithm from aft.js
  return calculatePLKScoreFromAft(seconds, age, gender);
}

/**
 * Get the minimum time required for a specific score
 * @param targetScore - Target score (0-100)
 * @param age - Age of the person
 * @param gender - Gender ('male' or 'female')
 * @returns Minimum time required in seconds
 */
export function getMinimumTimeForScore(targetScore: number, age: number, gender: 'male' | 'female'): number {
  if (targetScore < 0 || targetScore > 100) {
    return 0;
  }

  const ageGroup = getAgeGroup(age);
  const genderKey = gender === 'male' ? 'Male' : 'Female';
  
  const scoreTable = plk[ageGroup]?.[genderKey];

  if (!scoreTable) {
    return 0;
  }

  // Convert to array and sort by points descending
  const entries = Object.entries(scoreTable)
    .map(([timeStr, points]) => ({ seconds: timeToSeconds(timeStr as string), points: points as number }))
    .sort((a, b) => b.points - a.points);

  // Find exact match first
  const exactMatch = entries.find(entry => entry.points === targetScore);
  if (exactMatch) {
    return exactMatch.seconds;
  }

  // Find the entry with the closest higher score
  for (let i = 0; i < entries.length; i++) {
    const entry = entries[i];
    if (entry.points >= targetScore) {
      return entry.seconds;
    }
  }

  // If target score is lower than min, return time for min score
  return entries[entries.length - 1].seconds;
}

/**
 * Get performance category based on score
 * @param score - Score (0-100)
 * @returns Performance category and color
 */
export function getPLKPerformanceCategory(score: number): { category: string; color: string } {
  if (score >= 90) return { category: 'Excellent', color: 'text-green-600' };
  if (score >= 80) return { category: 'Very Good', color: 'text-blue-600' };
  if (score >= 70) return { category: 'Good', color: 'text-yellow-600' };
  if (score >= 60) return { category: 'Satisfactory', color: 'text-orange-600' };
  return { category: 'Unsatisfactory', color: 'text-red-600' };
}

/**
 * Format time from seconds to MM:SS format
 * @param seconds - Time in seconds
 * @returns Formatted time string
 */
export function formatPLKTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Parse time string to seconds
 * @param timeStr - Time string in MM:SS format
 * @returns Time in seconds
 */
export function parsePLKTime(timeStr: string): number {
  return timeToSeconds(timeStr);
}
