// Import functions from the main AFT data source
import {
  getAgeGroup,
  getHRPMinimumRequirements,
  getHRPMaximumRequirements,
  getMDLMinimumRequirements,
  getMDLMaximumRequirements,
  getSDCMinimumRequirements,
  getSDCMaximumRequirements,
  getPLKMinimumRequirements,
  getPLKMaximumRequirements,
  get2MRMinimumRequirements,
  get2MRMaximumRequirements
} from '@/data/aft';

// Helper function to convert seconds to time string
function secondsToTimeString(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}



export interface AFTStandards {
  ageGroup: number;
  gender: 'Male' | 'Female';
  standards: {
    deadlift: { min: number; max: number };
    pushups: { min: number; max: number };
    sprintDragCarry: { min: string; max: string };
    plank: { min: string; max: string };
    twoMileRun: { min: string; max: string };
  };
}

export const getAFTStandardsByAge = (age: number, gender: 'Male' | 'Female'): AFTStandards => {
  const ageGroup = getAgeGroup(age);

  // Convert gender to the format expected by the data functions
  const genderKey = gender === 'Male' ? 'male' : 'female';

  try {
    // Get minimum and maximum requirements from the main data source
    const hrpMin = getHRPMinimumRequirements(age, genderKey);
    const hrpMax = getHRPMaximumRequirements(age, genderKey);
    const mdlMin = getMDLMinimumRequirements(age, genderKey);
    const mdlMax = getMDLMaximumRequirements(age, genderKey);
    const sdcMin = getSDCMinimumRequirements(age, genderKey);
    const sdcMax = getSDCMaximumRequirements(age, genderKey);
    const plkMin = getPLKMinimumRequirements(age, genderKey);
    const plkMax = getPLKMaximumRequirements(age, genderKey);
    const tmrMin = get2MRMinimumRequirements(age, genderKey);
    const tmrMax = get2MRMaximumRequirements(age, genderKey);

    return {
      ageGroup,
      gender,
      standards: {
        deadlift: {
          min: mdlMin.minValue || 0,
          max: mdlMax.maxValue || 0
        },
        pushups: {
          min: hrpMin.minValue || 0,
          max: hrpMax.maxValue || 0
        },
        sprintDragCarry: {
          min: secondsToTimeString(sdcMin.minValue || 0),
          max: secondsToTimeString(sdcMax.maxValue || 0)
        },
        plank: {
          min: secondsToTimeString(plkMin.minValue || 0),
          max: secondsToTimeString(plkMax.maxValue || 0)
        },
        twoMileRun: {
          min: secondsToTimeString(tmrMin.minValue || 0),
          max: secondsToTimeString(tmrMax.maxValue || 0)
        }
      }
    };
  } catch (error) {
    console.error(`Error getting AFT standards for age ${age}, gender ${gender}:`, error);
    throw new Error(`No data found for age group ${ageGroup} and gender ${gender}`);
  }
};

// Helper function to get all available age groups
export const getAvailableAgeGroups = (): number[] => {
  return [17, 22, 27, 32, 37, 42, 47, 52, 57, 62];
};

// Helper function to get age range for a given age group
export const getAgeRangeForGroup = (ageGroup: number): string => {
  switch (ageGroup) {
    case 17: return '17-21';
    case 22: return '22-26';
    case 27: return '27-31';
    case 32: return '32-36';
    case 37: return '37-41';
    case 42: return '42-46';
    case 47: return '47-51';
    case 52: return '52-56';
    case 57: return '57-61';
    case 62: return '62+';
    default: return 'Unknown';
  }
};

// Helper function to validate age
export const isValidAge = (age: number): boolean => {
  return age >= 17 && age <= 100;
};

// Helper function to get event names
export const getEventNames = () => {
  return {
    deadlift: '3 Repetition Maximum Deadlift (MDL)',
    pushups: 'Hand-Release Push-Up (HRP)',
    sprintDragCarry: 'Sprint-Drag-Carry (SDC)',
    plank: 'Plank (PLK)',
    twoMileRun: '2-Mile Run (2MR)'
  };
};
