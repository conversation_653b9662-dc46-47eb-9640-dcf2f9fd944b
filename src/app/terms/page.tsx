import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, AlertTriangle, Shield, Scale, Users, Clock } from 'lucide-react';
import type { Metadata } from 'next';
import { siteConfig } from '@/config/site';

export const metadata: Metadata = {
  title: 'Terms of Service - AFT Calculator',
  description: 'Terms of Service for AFT Calculator - Learn about the terms and conditions for using our Army Fitness Test calculator.',
  keywords: 'AFT Calculator terms of service, terms and conditions, user agreement, legal terms',
};

export default function TermsPage() {
  const lastUpdated = "December 2024";

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-gray-50 to-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-4 bg-[#FFCC01] text-black border-[#FFCC01]">
              <FileText className="w-4 h-4 mr-1" />
              Legal Terms
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-black">
              Terms of <span className="text-[#FFCC01]">Service</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Please read these terms carefully before using our AFT Calculator service.
            </p>
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated}
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto space-y-8">
            
            {/* Acceptance of Terms */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Scale className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Acceptance of Terms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  By accessing and using AFT Calculator (&quot;the Service&quot;) at <strong>aftcalculator.online</strong>, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                </p>
              </CardContent>
            </Card>

            {/* Service Description */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <FileText className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Service Description
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  AFT Calculator is a free, web-based tool that provides:
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li>• Real-time Army Fitness Test (AFT) score calculations</li>
                  <li>• Educational resources about AFT standards and training</li>
                  <li>• General fitness and training information</li>
                </ul>
                <p className="text-gray-600 leading-relaxed">
                  All calculations are performed locally in your browser and no personal data is stored or transmitted.
                </p>
              </CardContent>
            </Card>

            {/* Disclaimer and Limitations */}
            <Card className="border-gray-200 border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <AlertTriangle className="w-6 h-6 mr-3 text-red-500" />
                  Important Disclaimer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">Unofficial Tool</h3>
                  <p className="text-red-700 leading-relaxed">
                    AFT Calculator is <strong>NOT affiliated with, endorsed by, or connected to the United States Army</strong> or any other military organization. This is an independent, unofficial tool created for educational and informational purposes only.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-black mb-3">Use at Your Own Risk</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Results are for reference only and should not be considered official</li>
                    <li>• Always verify calculations with official Army sources</li>
                    <li>• Consult qualified military personnel for authoritative guidance</li>
                    <li>• We make no guarantees about the accuracy of calculations</li>
                    <li>• Users are responsible for verifying all information</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* User Responsibilities */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Users className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  User Responsibilities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed mb-4">
                  By using our service, you agree to:
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li>• Use the service for lawful purposes only</li>
                  <li>• Not attempt to interfere with or disrupt the service</li>
                  <li>• Not use automated tools to access the service excessively</li>
                  <li>• Respect intellectual property rights</li>
                  <li>• Verify all calculations with official sources</li>
                  <li>• Not rely solely on our calculations for official purposes</li>
                </ul>
              </CardContent>
            </Card>

            {/* Intellectual Property */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Shield className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Intellectual Property
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  The content, design, and functionality of AFT Calculator are protected by copyright and other intellectual property laws. This includes but is not limited to:
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li>• Website design and user interface</li>
                  <li>• Calculation algorithms and formulas</li>
                  <li>• Text content and educational materials</li>
                  <li>• Graphics, logos, and visual elements</li>
                </ul>
                <p className="text-gray-600 leading-relaxed">
                  You may use the service for personal, non-commercial purposes. Any other use requires written permission.
                </p>
              </CardContent>
            </Card>

            {/* Limitation of Liability */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <AlertTriangle className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Limitation of Liability
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  To the fullest extent permitted by law, AFT Calculator and its operators shall not be liable for:
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li>• Any direct, indirect, incidental, or consequential damages</li>
                  <li>• Errors or inaccuracies in calculations or information</li>
                  <li>• Service interruptions or technical issues</li>
                  <li>• Any decisions made based on information from our service</li>
                  <li>• Loss of data or any other losses</li>
                </ul>
                <div className="bg-yellow-50 p-4 rounded-lg mt-4">
                  <p className="text-yellow-800 font-medium">
                    The service is provided &quot;as is&quot; without any warranties, express or implied.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Service Availability */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Clock className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Service Availability
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  We strive to maintain service availability but do not guarantee uninterrupted access. The service may be temporarily unavailable due to maintenance, updates, or technical issues. We reserve the right to modify, suspend, or discontinue the service at any time without notice.
                </p>
              </CardContent>
            </Card>

            {/* Privacy and Data */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Shield className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Privacy and Data
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  We respect your privacy and do not collect or store your personal fitness data. All calculations are performed locally in your browser. For detailed information about our data practices, please review our
                  <a href="/privacy" className="text-[#FFCC01] hover:underline ml-1">Privacy Policy</a>.
                </p>
              </CardContent>
            </Card>

            {/* Third-Party Services */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <FileText className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Third-Party Services
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  Our website uses third-party services for analytics and functionality:
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li>• <strong>Google Analytics:</strong> For website usage analytics</li>
                  <li>• <strong>Microsoft Clarity:</strong> For user experience analysis</li>
                </ul>
                <p className="text-gray-600 leading-relaxed">
                  These services have their own terms of service and privacy policies. We are not responsible for their practices or policies.
                </p>
              </CardContent>
            </Card>

            {/* Modifications to Terms */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Clock className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Modifications to Terms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting on this page. Your continued use of the service after any changes constitutes acceptance of the new terms. We encourage you to review these terms periodically.
                </p>
              </CardContent>
            </Card>

            {/* Governing Law */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Scale className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Governing Law
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  These terms shall be governed by and construed in accordance with the laws of the United States. Any disputes arising from these terms or your use of the service shall be subject to the jurisdiction of the appropriate courts.
                </p>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Users className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  If you have any questions about these Terms of Service, please contact us:
                </p>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 font-medium">AFT Calculator Team</p>
                  <p className="text-gray-600">Website: {siteConfig.domain}</p>
                  <p className="text-gray-600">Email: support@{siteConfig.domain}</p>
                  <p className="text-gray-600">For legal inquiries, please contact us using the information above.</p>
                </div>
              </CardContent>
            </Card>

            {/* Severability */}
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-xl font-semibold text-black">
                  <Scale className="w-6 h-6 mr-3 text-[#FFCC01]" />
                  Severability
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  If any provision of these terms is found to be unenforceable or invalid, that provision will be limited or eliminated to the minimum extent necessary so that the remaining terms will remain in full force and effect.
                </p>
              </CardContent>
            </Card>

            {/* Final Disclaimer */}
            <Card className="border-gray-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-red-800 mb-3">Final Reminder</h3>
                  <p className="text-sm text-red-700 leading-relaxed">
                    AFT Calculator is an <strong>unofficial tool</strong> and is <strong>not affiliated with the U.S. Army</strong>.
                    This service is provided for informational purposes only. Always consult official Army regulations
                    and qualified military personnel for authoritative guidance on Army Fitness Test requirements and standards.
                  </p>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </section>
    </div>
  );
}
