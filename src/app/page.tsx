import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calculator, Target, BookOpen, Shield } from 'lucide-react';
import Link from 'next/link';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import AFTCalculatorWrapper from '@/components/calculator/AFTCalculatorWrapper';

export const metadata: Metadata = {
  title: siteConfig.title,
  description: siteConfig.description,
  alternates: {
    canonical: siteConfig.url,
  },
  openGraph: {
    title: siteConfig.title,
    description: siteConfig.description,
    url: siteConfig.url,
    siteName: siteConfig.openGraph.siteName,
    images: [
      {
        url: `${siteConfig.url}/logo.png`,
        width: 1200,
        height: 630,
        alt: 'AFT Calculator - Army Fitness Test Score Calculator',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.title,
    description: siteConfig.description,
    images: [`${siteConfig.url}/logo.png`],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function Home() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AFT Calculator",
    "alternateName": "Army Fitness Test Calculator",
    "description": siteConfig.description,
    "url": siteConfig.url,
    "applicationCategory": "HealthApplication",
    "operatingSystem": "Web Browser",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "softwareVersion": "1.0",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "author": {
      "@type": "Organization",
      "name": siteConfig.organization.name,
      "url": siteConfig.url
    },
    "publisher": {
      "@type": "Organization",
      "name": siteConfig.organization.publisher,
      "url": siteConfig.url
    },
    "inLanguage": "en-US",
    "copyrightYear": new Date().getFullYear(),
    "datePublished": "2024-01-01",
    "dateModified": new Date().toISOString().split('T')[0]
  };

  return (
    <div className="bg-white">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-gray-50 to-white py-6 sm:py-10">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold mb-4 sm:mb-6 text-center">
              <span className="text-[#FFCC01] block">AFT Calculator</span>
              <span className="text-black block">Army Fitness Test Score Calculator</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-10 leading-relaxed px-2 sm:px-0">
              Calculate your AFT score instantly with our free AFT Calculator for Army Fitness Test. <br className="hidden sm:block" />
              Professional AFT Calculator with official 2025 standards, real-time analysis, and accurate results for all five test events.
              Our AFT Calculator provides the most reliable Army Fitness Test scoring. Check our <Link href="/aft-score-chart" className="text-[#FFCC01] hover:underline">AFT score chart</Link> for detailed standards.
            </p>
            <div className="flex flex-wrap justify-center gap-3">
              <Badge variant="secondary" className="bg-[#FFCC01]/10 text-[#FFCC01] border-[#FFCC01]/20 px-4 py-2">Free AFT Calculator</Badge>
              <Badge variant="secondary" className="bg-[#FFCC01]/10 text-[#FFCC01] border-[#FFCC01]/20 px-4 py-2">Official AFT Standards</Badge>
              <Badge variant="secondary" className="bg-[#FFCC01]/10 text-[#FFCC01] border-[#FFCC01]/20 px-4 py-2">Instant AFT Results</Badge>
              <Badge variant="secondary" className="bg-[#FFCC01]/10 text-[#FFCC01] border-[#FFCC01]/20 px-4 py-2">Mobile AFT Calculator</Badge>
            </div>
            {/* <div className="mt-8">
              <a href="#calculator" className="inline-flex items-center px-8 py-4 bg-[#FFCC01] text-black font-semibold rounded-lg hover:bg-[#FFCC01]/90 transition-colors text-lg">
                Start Calculating →
              </a>
            </div> */}
          </div>
        </div>
      </section>

      {/* About AFT Section - 移到前面，让用户先了解AFT */}
      {/* <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">About the Army Fitness Test (AFT)</h2>
              <p className="text-lg text-gray-600">
                Comprehensive physical fitness assessment for military readiness
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 shadow-sm">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <Shield className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">What is the AFT?</h3>
                    <p className="text-gray-600 leading-relaxed">
                      The Army Fitness Test (AFT) is a comprehensive physical fitness assessment used by the U.S. Army
                      to evaluate soldiers' physical readiness. Our AFT calculator provides accurate score calculations
                      based on official Army standards for all five test events.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 shadow-sm">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <Target className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">Five Test Events</h3>
                    <p className="text-gray-600 leading-relaxed">
                      The AFT consists of five events: 3 Repetition Maximum Deadlift, Hand-Release Push-Up,
                      Sprint-Drag-Carry, Plank, and 2-Mile Run. Each event
                      is scored from 0-100 points, with a minimum of 60 points required per event to pass.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-[#FFCC01]/5 to-[#FFCC01]/10 border border-[#FFCC01]/20 rounded-lg p-8">
              <div className="text-center">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-16 h-16 bg-[#FFCC01]/10 rounded-full flex items-center justify-center">
                    <Calculator className="h-8 w-8 text-[#FFCC01]" />
                  </div>
                </div>
                <h3 className="text-2xl font-semibold text-black mb-4">Start Your Fitness Assessment</h3>
                <p className="text-gray-600 leading-relaxed max-w-3xl mx-auto mb-6">
                  Use our free AFT calculator to determine your current fitness level, track your progress,
                  and prepare for your official Army Fitness Test. Our calculator uses the most up-to-date
                  scoring standards and provides instant, accurate results.
                </p>
                <div className="flex flex-wrap justify-center gap-3">
                  <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full border border-[#FFCC01]/20">
                    <svg className="h-4 w-4 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-sm text-gray-700">Official Standards</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full border border-[#FFCC01]/20">
                    <svg className="h-4 w-4 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span className="text-sm text-gray-700">Instant Results</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full border border-[#FFCC01]/20">
                    <svg className="h-4 w-4 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className="text-sm text-gray-700">Progress Tracking</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Main Calculator */}
      <section id="calculator">
        <div className="container mx-auto">
          {/* <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-black">AFT Score Calculator</h2>
            <p className="text-lg text-gray-600">
              Enter your performance data to calculate your official AFT score
            </p>
          </div> */}
          <AFTCalculatorWrapper />
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">How Our AFT Calculator Works</h2>
              <p className="text-lg text-gray-600">
                Simple, fast, and accurate AFT Calculator for Army Fitness Test assessment in three easy steps
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-[#FFCC01]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-[#FFCC01]">1</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3">Enter Your AFT Calculator Data</h3>
                <p className="text-gray-600">
                  Input your age, gender, and performance results into our AFT Calculator for each of the five Army Fitness Test events: deadlift, push-ups, sprint-drag-carry, plank, and 2-mile run
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-[#FFCC01]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-[#FFCC01]">2</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3">Get Instant AFT Calculator Results</h3>
                <p className="text-gray-600">
                  Our AFT Calculator instantly computes your scores using official Army Fitness Test standards and 2025 AFT Calculator scoring tables
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-[#FFCC01]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-[#FFCC01]">3</span>
                </div>
                <h3 className="text-xl font-semibold text-black mb-3">Track Your AFT Calculator Progress</h3>
                <p className="text-gray-600">
                  Monitor your Army Fitness Test improvements and prepare for your official AFT with our AFT Calculator score tracking features
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-[#FFCC01]/5 to-[#FFCC01]/10 rounded-lg border border-[#FFCC01]/20 p-8">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-black mb-4">Professional AFT Calculator Accuracy</h3>
                <p className="text-gray-600 leading-relaxed max-w-3xl mx-auto">
                  Our AFT Calculator uses the latest 2025 AFT standards and official Army scoring tables.
                  The AFT Calculator instantly provides accurate results for your Army Fitness Test, allowing you to track your progress
                  over time and stay on top of your military fitness goals with professional-grade AFT Calculator precision.
                  Visit our <Link href="/faq" className="text-[#FFCC01] hover:underline">FAQ section</Link> for common questions about AFT Calculator scoring.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">Key Features of Our AFT Calculator</h2>
              <p className="text-lg text-gray-600">
                Professional-grade AFT Calculator tools designed for accurate Army Fitness Test score assessment
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <Shield className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Accurate AFT Calculator Scoring</h3>
                    <p className="text-gray-600 text-sm">
                      Our AFT Calculator computes Army Fitness Test scores based on the latest 2025 AFT standards, ensuring reliable and precise AFT Calculator results.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <Target className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Comprehensive AFT Calculator Assessment</h3>
                    <p className="text-gray-600 text-sm">
                      Our AFT Calculator covers all five Army Fitness Test events with detailed performance metrics and real-time AFT Calculator scoring.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <BookOpen className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Promotion Points Insight</h3>
                    <p className="text-gray-600 text-sm">
                      Provides valuable insights into how your AFT Calculator score influences your career advancement and promotion points.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <Calculator className="h-6 w-6 text-[#FFCC01]" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Regular Updates</h3>
                    <p className="text-gray-600 text-sm">
                      Reflects the latest AFT Calculator regulations and scoring charts, keeping your fitness assessments up to date.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Mobile-Friendly Design</h3>
                    <p className="text-gray-600 text-sm">
                      Accessible on smartphones, tablets, and computers, making it easy to use from anywhere.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-2">Instant Results</h3>
                    <p className="text-gray-600 text-sm">
                      Get immediate feedback with real-time score updates and visual progress tracking.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">Why Choose Our AFT Calculator?</h2>
              <p className="text-lg text-gray-600">
                Professional Army Fitness Test features that set our AFT Calculator apart from other fitness calculators
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">Fast and Accurate AFT Calculator Results</h3>
                    <p className="text-gray-600">
                      Our AFT Calculator provides immediate, precise Army Fitness Test scores without any complicated setup. Get your AFT Calculator results in seconds.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">User-Friendly Interface</h3>
                    <p className="text-gray-600">
                      Simple design that focuses on essential inputs for a seamless user experience across all devices.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">Comprehensive Fitness Tracking</h3>
                    <p className="text-gray-600">
                      Monitor your progress over time with detailed analytics and performance insights for each event.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-[#FFCC01]/20 rounded-lg p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#FFCC01]/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-[#FFCC01]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-black mb-3">Professional-Grade Calculations</h3>
                    <p className="text-gray-600">
                      Built with the latest AFT Calculator guidelines and official Army standards for professional military fitness assessments.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Resources Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">Additional Resources</h2>
              <p className="text-lg text-gray-600">
                Everything you need for AFT Calculator success and military fitness preparation
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center border border-[#FFCC01] bg-white hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Target className="h-12 w-12 mx-auto text-[#FFCC01] mb-4" />
                  <CardTitle className="text-black">AFT Score Chart</CardTitle>
                  <CardDescription className="text-gray-600">
                    View detailed scoring standards by age group and gender for all AFT events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link href="/aft-score-chart" className="text-[#FFCC01] hover:text-[#FFCC01]/80 font-medium transition-colors">
                    View Score Chart →
                  </Link>
                </CardContent>
              </Card>

              <Card className="text-center border border-[#FFCC01] bg-white hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Calculator className="h-12 w-12 mx-auto text-[#FFCC01] mb-4" />
                  <CardTitle className="text-black">AFT Standards by Age</CardTitle>
                  <CardDescription className="text-gray-600">
                    Find your specific AFT requirements based on your age and gender
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link href="/aft-standards" className="text-[#FFCC01] hover:text-[#FFCC01]/80 font-medium transition-colors">
                    Check Standards →
                  </Link>
                </CardContent>
              </Card>

            
              <Card className="text-center border border-[#FFCC01] bg-white hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Calculator className="h-12 w-12 mx-auto text-[#FFCC01] mb-4" />
                  <CardTitle className="text-black">Training Tips</CardTitle>
                  <CardDescription className="text-gray-600">
                    Get expert training tips and workout plans to improve your AFT performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <span className="text-gray-500 text-sm">Coming Soon</span>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 bg-gradient-to-r from-[#FFCC01]/10 to-[#FFCC01]/5">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-black mb-4">
              Ready to Calculate Your Army Fitness Test Score?
            </h2>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Take control of your military fitness journey. Use our professional AFT Calculator
              to track your Army Fitness Test progress, identify areas for improvement, and achieve your AFT Calculator goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#calculator"
                className="inline-flex items-center px-8 py-4 bg-[#FFCC01] text-black font-semibold rounded-lg hover:bg-[#FFCC01]/90 transition-colors text-lg"
              >
                Start AFT Calculator Now
              </a>
              <Link
                href="/aft-score-chart"
                className="inline-flex items-center px-8 py-4 bg-white text-[#FFCC01] font-semibold rounded-lg border-2 border-[#FFCC01] hover:bg-[#FFCC01]/5 transition-colors text-lg"
              >
                View AFT Score Chart
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
