'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Home, Calculator, Target, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 404 Number */}
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-[#FFCC01] mb-4">404</h1>
          <h2 className="text-3xl md:text-4xl font-bold text-black mb-4">
            Page Not Found
          </h2>
          <p className="text-lg text-gray-600 mb-8 leading-relaxed">
            The page you&apos;re looking for doesn&apos;t exist or has been moved.
            Let&apos;s get you back on track with your fitness goals.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button asChild size="lg" className="bg-[#FFCC01] text-black hover:bg-[#FFCC01]/90">
            <Link href="/">
              <Home className="mr-2 h-5 w-5" />
              Go Home
            </Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="border-[#FFCC01] text-[#FFCC01] hover:bg-[#FFCC01]/5">
            <Link href="/#calculator">
              <Calculator className="mr-2 h-5 w-5" />
              AFT Calculator
            </Link>
          </Button>
        </div>

        {/* Helpful Links */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border border-[#FFCC01]/20 hover:shadow-lg transition-shadow h-full">
            <CardHeader className="text-center">
              <Calculator className="h-8 w-8 mx-auto text-[#FFCC01] mb-2" />
              <CardTitle className="text-black text-lg mb-2">AFT Calculator</CardTitle>
              <CardDescription className="text-gray-600 min-h-[3rem] flex items-center justify-center">
                Calculate your Army Fitness Test score instantly
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center mt-auto">
              <Link href="/#calculator" className="text-[#FFCC01] hover:text-[#FFCC01]/80 font-medium transition-colors">
                Start Calculating →
              </Link>
            </CardContent>
          </Card>

          <Card className="border border-[#FFCC01]/20 hover:shadow-lg transition-shadow h-full">
            <CardHeader className="text-center">
              <Target className="h-8 w-8 mx-auto text-[#FFCC01] mb-2" />
              <CardTitle className="text-black text-lg mb-2">Score Chart</CardTitle>
              <CardDescription className="text-gray-600 min-h-[3rem] flex items-center justify-center">
                View detailed AFT scoring standards by age and gender
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center mt-auto">
              <Link href="/aft-score-chart" className="text-[#FFCC01] hover:text-[#FFCC01]/80 font-medium transition-colors">
                View Chart →
              </Link>
            </CardContent>
          </Card>

          <Card className="border border-[#FFCC01]/20 hover:shadow-lg transition-shadow h-full">
            <CardHeader className="text-center">
              <svg className="h-8 w-8 mx-auto text-[#FFCC01] mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <CardTitle className="text-black text-lg mb-2">FAQ</CardTitle>
              <CardDescription className="text-gray-600 min-h-[3rem] flex items-center justify-center">
                Find answers to common AFT questions
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center mt-auto">
              <Link href="/faq" className="text-[#FFCC01] hover:text-[#FFCC01]/80 font-medium transition-colors">
                Read FAQ →
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Back Button */}
        <div className="mt-12">
          <Button 
            variant="ghost" 
            onClick={() => window.history.back()}
            className="text-gray-600 hover:text-black"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
}
