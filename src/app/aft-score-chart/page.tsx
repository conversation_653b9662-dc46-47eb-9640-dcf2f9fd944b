import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import ScoreChartSection from '@/components/aft-score-chart/ScoreChartSection';
import FAQSection from '@/components/aft-score-chart/FAQSection';
import ScrollToTop from '@/components/ui/scroll-to-top';
import { getCanonicalUrl } from '@/config/site';
import { AdUnitContainer } from '@/components/ad/ad-unit-container';

export const metadata: Metadata = {
  title: 'AFT Score Chart 2025 - Army Fitness Test Standards & Scoring Tables | AFT Calculator',
  description: 'Complete AFT Score Chart 2025 with detailed scoring tables for all Army Fitness Test events including deadlift, push-ups, sprint-drag-carry, plank, and 2-mile run. Official AFT standards and performance benchmarks for military fitness.',
  keywords: 'AFT score chart 2025, Army Fitness Test chart, AFT standards, AFT scoring table, Army fitness test 2025, AFT performance chart, military fitness standards, AFT calculator chart',
  openGraph: {
    title: 'AFT Score Chart 2025 - Army Fitness Test Standards & Scoring Tables',
    description: 'Complete AFT Score Chart 2025 with detailed scoring tables for all Army Fitness Test events. Official AFT standards and performance benchmarks.',
    url: getCanonicalUrl('/aft-score-chart'),
    type: 'website',
  },
  twitter: {
    title: 'AFT Score Chart 2025 - Army Fitness Test Standards & Scoring Tables',
    description: 'Complete AFT Score Chart 2025 with detailed scoring tables for all Army Fitness Test events. Official AFT standards and performance benchmarks.',
  },
  alternates: {
    canonical: getCanonicalUrl('/aft-score-chart'),
  },
};

export default function AFTScoreChartPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "AFT Score Chart 2025",
    "description": "Complete AFT Score Chart 2025 with detailed scoring tables for all Army Fitness Test events including MDL, HRP, SDC, PLK, and 2MR.",
    "url": getCanonicalUrl('/aft-score-chart'),
    "mainEntity": {
      "@type": "Article",
      "headline": "AFT Score Chart 2025 - Army Fitness Test Standards Chart",
      "description": "Official AFT scoring standards and performance benchmarks for the Army Fitness Test 2025.",
      "author": {
        "@type": "Organization",
        "name": "AFT Calculator Team"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AFT Calculator"
      }
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {/* Breadcrumb */}
      {/* <nav className="bg-gray-50 border-b border-gray-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center space-x-2 text-sm">
            <Link href="/" className="flex items-center text-gray-600 hover:text-[#FFCC01] transition-colors">
              <Home className="h-4 w-4 mr-1" />
              Home
            </Link>
            <ChevronRight className="h-4 w-4 text-gray-400" />
            <span className="text-[#FFCC01] font-medium">AFT Score Chart 2025</span>
          </div>
        </div>
      </nav> */}

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#FFCC01]/10 to-[#FFCC01]/5 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              AFT Score Chart 2025 - Army Fitness Test Standards
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto mb-8">
              The Army Fitness Test (AFT) effective on June 1, 2025. Check the new AFT score chart now!
              Complete scoring tables for all five AFT events with official Army fitness test standards.
            </p>
            <Link href="/aft-standards" className="inline-flex items-center px-6 py-3 bg-[#FFCC01] text-black font-semibold rounded-lg hover:bg-[#FFCC01]/90 transition-colors">
              <span>Check Your Standards</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Top Banner Ad */}
      <section className="py-4 bg-white">
        <div className="container mx-auto px-4">
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_LEADERBOARD || "6979167199"}
            size="leaderboard"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-4xl mx-auto hidden md:block"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[90px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Score Chart Top (728x90)
            </div>
          </AdUnitContainer>

          {/* Mobile Banner Ad */}
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER || "7919911443"}
            size="mobile-banner"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-sm mx-auto md:hidden"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[50px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Score Chart Mobile Top (300x50)
            </div>
          </AdUnitContainer>
        </div>
      </section>

      {/* Score Chart Sections */}
      <ScoreChartSection />

      {/* Mid-Content Ad */}
      <section className="py-6 bg-gray-50">
        <div className="container mx-auto px-4">
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_MEDIUM_RECTANGLE || "7776123983"}
            size="medium-rectangle"
            containerStyle="card"
            responsive={false}
            className="w-full max-w-sm mx-auto"
            adClassName="mx-auto"
            adLabel="Advertisement"
          >
            <div className="bg-muted w-full h-[250px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Score Chart Mid (300x250)
            </div>
          </AdUnitContainer>
        </div>
      </section>

      {/* Description Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="border-t-4 border-[#FFCC01] mb-8"></div>
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed mb-6">
                Understanding the <strong>AFT score chart</strong> is essential for achieving high performance on the{' '}
                <strong>Army Fitness Test (AFT)</strong>. This page provides a detailed overview of the{' '}
                <strong>2025 AFT score chart</strong>, including scoring tables, performance benchmarks, and point calculations. 
                Whether you are an active-duty soldier, a reservist, or a fitness enthusiast, mastering the AFT standards 
                is crucial for both physical readiness and career advancement.
              </p>
            </div>
            <div className="border-t-4 border-[#FFCC01] mt-8"></div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection />

      {/* Call to Action */}
      <section className="py-16 bg-[#FFCC01]/10">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Calculate Your Army Fitness Test Score?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Stay prepared and perform your best by using the AFT score chart as your guide.
            For more accurate AFT score calculations, try our Army Fitness Test calculator today.
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-[#FFCC01] text-black font-semibold rounded-lg hover:bg-[#FFCC01]/90 transition-colors text-lg"
          >
            Use AFT Calculator
          </Link>
        </div>
      </section>

      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
}
