'use client';

import { useState } from 'react';

export default function EmbedExamplesPage() {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // 设置页面标题
  if (typeof document !== 'undefined') {
    document.title = 'Embed Examples - AFT Calculator';
  }
  const embedCalculatorUrl = process.env.NODE_ENV === 'production'
    ? 'https://aftcalculator.online/embed-aft-calculator'
    : 'http://localhost:3000/embed-aft-calculator';

  const embedStandardsUrl = process.env.NODE_ENV === 'production'
    ? 'https://aftcalculator.online/embed-aft-standards'
    : 'http://localhost:3000/embed-aft-standards';

  const calculatorExamples = [
    {
      title: 'AFT Calculator - Hidden Brand',
      description: '完全隐藏品牌标识，最干净的嵌入体验',
      url: `${embedCalculatorUrl}?brand=hidden`,
      code: `<iframe src="${embedCalculatorUrl}?brand=hidden" width="100%" height="800" frameborder="0"></iframe>`
    },
    {
      title: 'AFT Calculator - Badge Style',
      description: '徽章样式的品牌标识，美观且不突兀',
      url: `${embedCalculatorUrl}?brand=badge`,
      code: `<iframe src="${embedCalculatorUrl}?brand=badge" width="100%" height="800" frameborder="0"></iframe>`
    }
  ];

  const standardsExamples = [
    {
      title: 'AFT Standards - Hidden Brand',
      description: '完全隐藏品牌标识的标准查询工具',
      url: `${embedStandardsUrl}?brand=hidden`,
      code: `<iframe src="${embedStandardsUrl}?brand=hidden" width="100%" height="600" frameborder="0"></iframe>`
    },
    {
      title: 'AFT Standards - Badge Style',
      description: '徽章样式的标准查询工具',
      url: `${embedStandardsUrl}?brand=badge`,
      code: `<iframe src="${embedStandardsUrl}?brand=badge" width="100%" height="600" frameborder="0"></iframe>`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">AFT Embed Widgets Examples</h1>
          <p className="text-lg text-gray-600">
            Choose the widget and brand style that best fits your website design
          </p>
        </div>

        {/* AFT Calculator Examples */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">AFT Score Calculator</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {calculatorExamples.map((example, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{example.title}</h3>
                <p className="text-gray-600 mb-4">{example.description}</p>
                
                <div className="bg-gray-100 rounded-md p-3">
                  <code className="text-sm text-gray-800 break-all">
                    {example.code}
                  </code>
                </div>
                
                <div className="mt-4 flex gap-3">
                  <a
                    href={example.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-[#FFCC01] text-black font-medium rounded-md hover:bg-[#FFCC01]/90 transition-colors"
                  >
                    预览
                  </a>
                  <button
                    onClick={async () => {
                      try {
                        await navigator.clipboard.writeText(example.code);
                        setCopiedIndex(index);
                        setTimeout(() => setCopiedIndex(null), 2000);
                      } catch (err) {
                        console.error('Failed to copy:', err);
                      }
                    }}
                    className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-md hover:bg-gray-300 transition-colors"
                  >
                    {copiedIndex === index ? '已复制!' : '复制代码'}
                  </button>
                </div>
              </div>
              
              <div className="h-96 bg-gray-50">
                <iframe
                  src={example.url}
                  className="w-full h-full border-0"
                  title={example.title}
                />
              </div>
            </div>
          ))}
        </div>
        </div>

        {/* AFT Standards Examples */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">AFT Standards Lookup</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {standardsExamples.map((example, index) => (
              <div key={index + 100} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{example.title}</h3>
                  <p className="text-gray-600 mb-4">{example.description}</p>

                  <div className="bg-gray-100 rounded-md p-3">
                    <code className="text-sm text-gray-800 break-all">
                      {example.code}
                    </code>
                  </div>

                  <div className="mt-4 flex gap-3">
                    <a
                      href={example.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-[#FFCC01] text-black font-medium rounded-md hover:bg-[#FFCC01]/90 transition-colors"
                    >
                      预览
                    </a>
                    <button
                      onClick={async () => {
                        try {
                          await navigator.clipboard.writeText(example.code);
                          setCopiedIndex(index + 100);
                          setTimeout(() => setCopiedIndex(null), 2000);
                        } catch (err) {
                          console.error('Failed to copy:', err);
                        }
                      }}
                      className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-md hover:bg-gray-300 transition-colors"
                    >
                      {copiedIndex === index + 100 ? '已复制!' : '复制代码'}
                    </button>
                  </div>
                </div>

                <div className="h-80 bg-gray-50">
                  <iframe
                    src={example.url}
                    className="w-full h-full border-0"
                    title={example.title}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">使用说明</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">URL 参数</h3>
              <div className="bg-gray-100 rounded-md p-4">
                <p className="text-sm text-gray-700 mb-2">通过添加 <code className="bg-white px-2 py-1 rounded">brand</code> 参数来控制品牌标识样式：</p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• <code className="bg-white px-2 py-1 rounded">?brand=hidden</code> - 完全隐藏品牌标识</li>
                  <li>• <code className="bg-white px-2 py-1 rounded">?brand=minimal</code> - 最小化显示</li>
                  <li>• <code className="bg-white px-2 py-1 rounded">?brand=badge</code> - 徽章样式（默认）</li>
                  <li>• <code className="bg-white px-2 py-1 rounded">?brand=subtle</code> - 微妙样式</li>
                </ul>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">推荐设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h4 className="font-medium text-blue-900 mb-2">商业网站</h4>
                  <p className="text-sm text-blue-700">推荐使用 <code>hidden</code> 或 <code>minimal</code> 样式，保持品牌一致性</p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h4 className="font-medium text-green-900 mb-2">教育/博客网站</h4>
                  <p className="text-sm text-green-700">推荐使用 <code>badge</code> 或 <code>subtle</code> 样式，既美观又给予适当的归属</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
