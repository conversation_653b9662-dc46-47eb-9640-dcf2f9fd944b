import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Target, Users, Calendar, Trophy } from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AFT Scoring Standards - Army Fitness Test Standards by Age and Gender',
  description: 'Complete AFT scoring standards and requirements. View official Army Fitness Test standards by age group and gender for all test events.',
};

// const ageGroups = ['17-21', '22-26', '27-31', '32-36', '37-41', '42-46', '47-51', '52-56', '57-61', '62+'];

const eventInfo = [
  {
    name: '3 Repetition Maximum Deadlift (MDL)',
    unit: 'lbs',
    description: 'Maximum weight lifted for 3 repetitions',
    icon: '🏋️',
  },
  {
    name: 'Hand-Release Push-Up (HRP)',
    unit: 'reps',
    description: 'Push-ups with hand release at bottom',
    icon: '💪',
  },
  {
    name: 'Sprint-Drag-Carry (SDC)',
    unit: 'MM:SS',
    description: 'Timed sprint, drag, and carry course',
    icon: '🏃',
  },
  {
    name: 'Plank (PLK)',
    unit: 'MM:SS',
    description: 'Maximum plank hold time',
    icon: '🧘',
  },
  {
    name: '2-Mile Run (2MR)',
    unit: 'MM:SS',
    description: 'Timed 2-mile run',
    icon: '🏃‍♂️',
  },
];

export default function StandardsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">AFT Scoring Standards</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Official Army Fitness Test scoring standards by age group and gender
        </p>
        <div className="flex flex-wrap justify-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Target className="h-3 w-3" />
            Official Standards
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            All Age Groups
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Updated 2025
          </Badge>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card>
          <CardHeader className="text-center">
            <Trophy className="h-8 w-8 mx-auto text-primary mb-2" />
            <CardTitle>Minimum Passing</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <div className="text-2xl font-bold text-primary">60 Points</div>
            <p className="text-sm text-muted-foreground">Per event minimum</p>
            <div className="text-lg font-semibold mt-2">300 Total</div>
            <p className="text-sm text-muted-foreground">Overall minimum</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Target className="h-8 w-8 mx-auto text-primary mb-2" />
            <CardTitle>Maximum Score</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <div className="text-2xl font-bold text-primary">100 Points</div>
            <p className="text-sm text-muted-foreground">Per event maximum</p>
            <div className="text-lg font-semibold mt-2">500 Total</div>
            <p className="text-sm text-muted-foreground">Perfect score</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Users className="h-8 w-8 mx-auto text-primary mb-2" />
            <CardTitle>Test Events</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <div className="text-2xl font-bold text-primary">5 Events</div>
            <p className="text-sm text-muted-foreground">Total test components</p>
            <div className="text-lg font-semibold mt-2">All Required</div>
            <p className="text-sm text-muted-foreground">Must pass each event</p>
          </CardContent>
        </Card>
      </div>

      {/* Test Events Overview */}
      <Card className="mb-12">
        <CardHeader>
          <CardTitle>AFT Test Events</CardTitle>
          <CardDescription>
            Overview of all five Army Fitness Test events and their requirements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {eventInfo.map((event, index) => (
              <div key={index} className="flex items-start space-x-3 p-4 border rounded-lg">
                <div className="text-2xl">{event.icon}</div>
                <div>
                  <h3 className="font-semibold">{event.name}</h3>
                  <p className="text-sm text-muted-foreground">{event.description}</p>
                  <Badge variant="secondary" className="mt-1">
                    Measured in {event.unit}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Score Categories */}
      <Card className="mb-12">
        <CardHeader>
          <CardTitle>Score Categories</CardTitle>
          <CardDescription>
            Performance categories based on individual event scores
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Score Range</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Color Code</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">90-100</TableCell>
                <TableCell>
                  <Badge className="bg-green-100 text-green-800">Excellent</Badge>
                </TableCell>
                <TableCell>Outstanding performance</TableCell>
                <TableCell>
                  <div className="w-4 h-4 bg-green-600 rounded"></div>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">80-89</TableCell>
                <TableCell>
                  <Badge className="bg-blue-100 text-blue-800">Very Good</Badge>
                </TableCell>
                <TableCell>Above average performance</TableCell>
                <TableCell>
                  <div className="w-4 h-4 bg-blue-600 rounded"></div>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">70-79</TableCell>
                <TableCell>
                  <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
                </TableCell>
                <TableCell>Satisfactory performance</TableCell>
                <TableCell>
                  <div className="w-4 h-4 bg-yellow-600 rounded"></div>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">60-69</TableCell>
                <TableCell>
                  <Badge className="bg-orange-100 text-orange-800">Satisfactory</Badge>
                </TableCell>
                <TableCell>Minimum passing performance</TableCell>
                <TableCell>
                  <div className="w-4 h-4 bg-orange-600 rounded"></div>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">0-59</TableCell>
                <TableCell>
                  <Badge className="bg-red-100 text-red-800">Unsatisfactory</Badge>
                </TableCell>
                <TableCell>Below minimum standard</TableCell>
                <TableCell>
                  <div className="w-4 h-4 bg-red-600 rounded"></div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
