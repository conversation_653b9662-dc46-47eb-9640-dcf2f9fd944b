import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BookOpen, Target, Clock, TrendingUp, Du<PERSON>bell, Heart, Zap } from 'lucide-react';
// import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AFT Training Guide - Army Fitness Test Workout Plans and Tips',
  description: 'Comprehensive AFT training guide with workout plans, exercise techniques, and preparation tips for all six Army Fitness Test events.',
  keywords: 'AFT training, Army Fitness Test training, AFT workout plan, military fitness training, AFT preparation',
};

const trainingEvents = [
  {
    name: '3 Repetition Maximum Deadlift',
    icon: '🏋️',
    description: 'Build maximum strength with progressive overload',
    tips: [
      'Focus on proper form and technique',
      'Progressive overload training',
      'Include accessory exercises',
      'Allow adequate recovery time'
    ],
    exercises: [
      'Conventional Deadlifts',
      'Romanian Deadlifts',
      'Rack Pulls',
      'Hip Thrusts',
      'Glute Bridges'
    ]
  },
  {
    name: 'Standing Power Throw',
    icon: '⚾',
    description: 'Develop explosive power and coordination',
    tips: [
      'Practice throwing technique',
      'Build core and shoulder strength',
      'Work on hip drive',
      'Focus on follow-through'
    ],
    exercises: [
      'Medicine Ball Throws',
      'Overhead Press',
      'Rotational Throws',
      'Plyometric Exercises',
      'Core Strengthening'
    ]
  },
  {
    name: 'Hand-Release Push-Up',
    icon: '💪',
    description: 'Build upper body endurance and strength',
    tips: [
      'Master proper form first',
      'Build up volume gradually',
      'Include push-up variations',
      'Strengthen supporting muscles'
    ],
    exercises: [
      'Standard Push-ups',
      'Incline Push-ups',
      'Decline Push-ups',
      'Diamond Push-ups',
      'Tricep Dips'
    ]
  },
  {
    name: 'Sprint-Drag-Carry',
    icon: '🏃',
    description: 'Develop anaerobic power and functional strength',
    tips: [
      'Practice the exact movements',
      'Build anaerobic capacity',
      'Improve transition speed',
      'Focus on technique under fatigue'
    ],
    exercises: [
      'Sprint Intervals',
      'Sled Drags',
      'Farmer\'s Walks',
      'Lateral Shuffles',
      'Burpees'
    ]
  },
  {
    name: 'Leg Tuck',
    icon: '🤸',
    description: 'Build core strength and grip endurance',
    tips: [
      'Start with assisted variations',
      'Build grip strength',
      'Focus on controlled movement',
      'Strengthen hip flexors'
    ],
    exercises: [
      'Hanging Knee Raises',
      'Dead Hangs',
      'Assisted Leg Tucks',
      'V-ups',
      'Planks'
    ]
  },
  {
    name: '2-Mile Run',
    icon: '🏃‍♂️',
    description: 'Build cardiovascular endurance and speed',
    tips: [
      'Build aerobic base first',
      'Include interval training',
      'Practice race pace',
      'Focus on consistent pacing'
    ],
    exercises: [
      'Long Slow Distance',
      'Tempo Runs',
      'Interval Training',
      'Fartlek Training',
      'Hill Repeats'
    ]
  }
];

const trainingPlans = [
  {
    title: 'Beginner (8-Week Plan)',
    description: 'For those new to military fitness training',
    duration: '8 weeks',
    frequency: '4-5 days/week',
    focus: 'Building base fitness and learning proper form',
    badge: 'Beginner',
    color: 'bg-green-100 text-green-800'
  },
  {
    title: 'Intermediate (12-Week Plan)',
    description: 'For those with basic fitness foundation',
    duration: '12 weeks',
    frequency: '5-6 days/week',
    focus: 'Improving performance in all events',
    badge: 'Intermediate',
    color: 'bg-blue-100 text-blue-800'
  },
  {
    title: 'Advanced (16-Week Plan)',
    description: 'For experienced athletes seeking maximum scores',
    duration: '16 weeks',
    frequency: '6 days/week',
    focus: 'Peak performance and score optimization',
    badge: 'Advanced',
    color: 'bg-purple-100 text-purple-800'
  }
];

export default function TrainingPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">AFT Training Guide</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Comprehensive training plans and techniques for Army Fitness Test success
        </p>
        <div className="flex flex-wrap justify-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            Expert Guidance
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Target className="h-3 w-3" />
            Event-Specific
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            Progressive Plans
          </Badge>
        </div>
      </div>

      {/* Training Plans Overview */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Training Plans</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {trainingPlans.map((plan, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <Badge className={plan.color} variant="secondary">
                  {plan.badge}
                </Badge>
                <CardTitle className="mt-2">{plan.title}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <Clock className="h-4 w-4 mx-auto mb-1" />
                    <div className="font-semibold">{plan.duration}</div>
                  </div>
                  <div>
                    <Dumbbell className="h-4 w-4 mx-auto mb-1" />
                    <div className="font-semibold">{plan.frequency}</div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">{plan.focus}</p>
                <Button className="w-full" variant="outline">
                  View Plan Details
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Event-Specific Training */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Event-Specific Training</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {trainingEvents.map((event, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{event.icon}</span>
                  <div>
                    <CardTitle className="text-lg">{event.name}</CardTitle>
                    <CardDescription>{event.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Target className="h-4 w-4 mr-1" />
                    Training Tips
                  </h4>
                  <ul className="text-sm space-y-1">
                    {event.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="text-muted-foreground">• {tip}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Dumbbell className="h-4 w-4 mr-1" />
                    Key Exercises
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {event.exercises.map((exercise, exerciseIndex) => (
                      <Badge key={exerciseIndex} variant="secondary" className="text-xs">
                        {exercise}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Training Principles */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Training Principles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardHeader>
              <TrendingUp className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>Progressive Overload</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Gradually increase training intensity, volume, or complexity to continuously improve performance.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Heart className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>Recovery</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Allow adequate rest between sessions for adaptation and injury prevention.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Zap className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>Specificity</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Train movements and energy systems specific to AFT requirements for optimal transfer.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Nutrition and Recovery */}
      <Card>
        <CardHeader>
          <CardTitle>Nutrition and Recovery</CardTitle>
          <CardDescription>
            Essential guidelines for supporting your training and performance
          </CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-3">Nutrition Guidelines</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Maintain adequate protein intake (0.8-1.2g per lb bodyweight)</li>
              <li>• Stay hydrated throughout training</li>
              <li>• Time carbohydrate intake around workouts</li>
              <li>• Include anti-inflammatory foods</li>
              <li>• Consider micronutrient needs</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-3">Recovery Strategies</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Prioritize 7-9 hours of quality sleep</li>
              <li>• Include active recovery days</li>
              <li>• Use mobility and stretching routines</li>
              <li>• Monitor training load and stress</li>
              <li>• Consider massage and foam rolling</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
