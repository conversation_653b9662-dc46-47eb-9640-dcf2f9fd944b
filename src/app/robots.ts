import { MetadataRoute } from 'next';
import { siteConfig, getSitemapUrl } from '@/config/site';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = siteConfig.url;

  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/api/',
        '/admin/',
        '/_next/',
        '/private/',
        '/*.json$',  // 阻止JSON文件被索引
        '/embed-*',  // 阻止embed页面被单独索引（避免重复内容）
      ],
    },
    sitemap: getSitemapUrl(),
    host: baseUrl,
  };
}
