import React from 'react';
import { Metadata } from 'next';
import AFTStandardsCalculator from '@/components/aft-standards/AFTStandardsCalculator';
import { getCanonicalUrl } from '@/config/site';
import { AdUnitContainer } from '@/components/ad/ad-unit-container';

export const metadata: Metadata = {
  title: 'AFT Calculator Standards by Age & Gender - Army Fitness Test Requirements',
  description: 'Find your specific AFT Calculator standards by age and gender. View minimum and maximum scores for all Army Fitness Test events using our AFT Calculator based on your age group.',
  openGraph: {
    title: 'AFT Standards by Age & Gender - Army Fitness Test Requirements',
    description: 'Find your specific AFT standards by age and gender. View minimum and maximum scores for all Army Fitness Test events based on your age group.',
    url: getCanonicalUrl('/aft-standards'),
  },
  twitter: {
    title: 'AFT Standards by Age & Gender - Army Fitness Test Requirements',
    description: 'Find your specific AFT standards by age and gender. View minimum and maximum scores for all Army Fitness Test events based on your age group.',
  },
  alternates: {
    canonical: getCanonicalUrl('/aft-standards'),
  },
};

export default function AFTStandardsPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "AFT Standards by Age & Gender",
    "description": "Find your AFT standards by age and gender. Get minimum and maximum scores for all Army Fitness Test events based on your age group.",
    "url": getCanonicalUrl('/aft-standards'),
    "mainEntity": {
      "@type": "Article",
      "headline": "AFT Standards by Age & Gender - Army Fitness Test Requirements",
      "description": "Complete guide to AFT standards organized by age groups and gender for all Army Fitness Test events.",
      "author": {
        "@type": "Organization",
        "name": "AFT Calculator Team"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AFT Calculator"
      }
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      {/* Page Header */}
      <section className="py-8 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl font-bold mb-4 text-black">
              AFT Calculator Standards
            </h1>
            <p className="text-lg text-gray-600 leading-relaxed">
              Enter your age and gender to find your specific Army Fitness Test standards using our AFT Calculator
            </p>
          </div>
        </div>
      </section>

      {/* Top Banner Ad */}
      <section className="py-4 bg-white">
        <div className="container mx-auto px-4">
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_LEADERBOARD || "6979167199"}
            size="leaderboard"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-4xl mx-auto hidden md:block"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[90px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Standards Top (728x90)
            </div>
          </AdUnitContainer>

          {/* Mobile Banner Ad */}
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER || "7919911443"}
            size="mobile-banner"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-sm mx-auto md:hidden"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[50px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Standards Mobile Top (300x50)
            </div>
          </AdUnitContainer>
        </div>
      </section>

      {/* Main Calculator */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <AFTStandardsCalculator />
        </div>
      </section>

      {/* Mid-Content Ad */}
      <section className="py-6 bg-gray-50">
        <div className="container mx-auto px-4">
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_MEDIUM_RECTANGLE || "7776123983"}
            size="medium-rectangle"
            containerStyle="card"
            responsive={false}
            className="w-full max-w-sm mx-auto"
            adClassName="mx-auto"
            adLabel="Advertisement"
          >
            <div className="bg-muted w-full h-[250px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Standards Mid (300x250)
            </div>
          </AdUnitContainer>
        </div>
      </section>

      {/* Information Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Understanding AFT Calculator Standards</h2>
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed mb-6">
                The <strong>AFT Calculator standards</strong> vary by age group and gender to ensure fair
                assessment across all demographics. Our AFT Calculator provides specific minimum and maximum score requirements
                for the five AFT events: 3 Repetition Maximum Deadlift, Hand-Release Push-Up, Sprint-Drag-Carry,
                Plank, and 2-Mile Run.
              </p>
              <p className="text-gray-700 leading-relaxed mb-6">
                Understanding your specific <strong>AFT Calculator standards by age and gender</strong> is crucial for:
              </p>
              <ul className="list-disc pl-6 mb-6 text-gray-700">
                <li>Setting realistic fitness goals</li>
                <li>Tracking your progress effectively</li>
                <li>Preparing for official AFT assessments</li>
                <li>Understanding promotion point requirements</li>
                <li>Planning targeted training programs</li>
              </ul>
              <p className="text-gray-700 leading-relaxed">
                Use the AFT Calculator above to find your specific standards and see what scores you need to achieve
                for passing, good, and excellent performance levels in each event with our AFT Calculator.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Bottom Banner Ad */}
      <section className="py-6 bg-white border-t border-gray-100">
        <div className="container mx-auto px-4">
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_LEADERBOARD || "6979167199"}
            size="leaderboard"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-4xl mx-auto hidden md:block"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[90px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Standards Bottom (728x90)
            </div>
          </AdUnitContainer>

          {/* Mobile Bottom Banner Ad */}
          <AdUnitContainer
            adUnitId={process.env.NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER || "7919911443"}
            size="mobile-banner"
            containerStyle="none"
            responsive={false}
            className="w-full max-w-sm mx-auto md:hidden"
            adClassName="mx-auto"
          >
            <div className="bg-muted w-full h-[50px] text-sm text-muted-foreground flex items-center justify-center rounded border">
              Advertisement - Standards Mobile Bottom (300x50)
            </div>
          </AdUnitContainer>
        </div>
      </section>
    </div>
  );
}
