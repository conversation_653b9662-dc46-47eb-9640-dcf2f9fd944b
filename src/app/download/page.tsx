import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Star,
  Calculator,
  Zap,
  Shield,
  Apple
} from 'lucide-react';
import type { Metadata } from 'next';
import QRCodeDownload from '@/components/download/QRCodeDownload';
import DownloadButton from '@/components/download/DownloadButton';
import { siteConfig } from '@/config/site';

export const metadata: Metadata = {
  title: 'Download AFT Calculator iOS App - Army Fitness Test Mobile App',
  description: 'Download the official AFT Calculator iOS app for iPhone and iPad. Calculate your Army Fitness Test score on the go with our professional mobile application.',
  keywords: 'AFT Calculator iOS app, Army Fitness Test mobile app, AFT app download, iPhone AFT calculator, iPad fitness app',
};

const appStoreUrl = siteConfig.appStore.iosAppUrl;

const features = [
  {
    icon: Calculator,
    title: 'Accurate Calculation',
    description: 'Precise scoring algorithm based on latest AFT standards for real-time military fitness test results'
  },
  {
    icon: Zap,
    title: 'Real-time Feedback',
    description: 'Instant display of individual event scores and overall performance to track training effectiveness'
  },
  {
    icon: Shield,
    title: 'Offline Usage',
    description: 'No internet connection required - calculate your AFT scores anytime, anywhere'
  },
  {
    icon: Star,
    title: 'Professional Tool',
    description: 'Recommended by military fitness experts, designed for soldiers and fitness enthusiasts'
  }
];

export default function DownloadPage() {

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-gray-50 to-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="mb-8">
              <Badge className="mb-4 bg-[#FFCC01] text-black border-[#FFCC01]">
                <Apple className="w-4 h-4 mr-1" />
                iOS App Available
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-black">
                AFT Calculator
                <span className="text-[#FFCC01]"> Mobile App</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Calculate your Army Fitness Test scores on the go <br />
                Professional, accurate, and convenient mobile AFT calculator
              </p>
            </div>

            {/* App Store Button and QR Code */}
            <QRCodeDownload appStoreUrl={appStoreUrl} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-black">
                Why Choose Our <span className="text-[#FFCC01]">Mobile App</span>
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Professional AFT calculator designed specifically for military personnel and fitness enthusiasts, providing accurate and convenient mobile experience
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 max-w-5xl mx-auto">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} className="border-gray-200 hover:border-[#FFCC01]/50 transition-all duration-300 hover:shadow-xl hover:scale-105 h-full group">
                    <CardHeader className="text-center pb-4">
                      <div className="mx-auto mb-4 w-16 h-16 bg-[#FFCC01]/10 group-hover:bg-[#FFCC01]/20 rounded-xl flex items-center justify-center transition-all duration-300">
                        <Icon className="w-8 h-8 text-[#FFCC01] group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <CardTitle className="text-xl font-semibold text-black mb-2 group-hover:text-[#FFCC01] transition-colors duration-300">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-gray-600 text-center leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* App Preview Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-black">
              Start Using <span className="text-[#FFCC01]">AFT Calculator</span> Today
            </h2>
            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Download our iOS app for a smoother mobile experience. Whether you&apos;re at the gym, training field, or home, calculate and track your AFT scores anytime.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-8">
              <DownloadButton appStoreUrl={appStoreUrl} />
              
              <div className="flex items-center space-x-2 text-gray-600">
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <Star className="w-5 h-5 text-[#FFCC01] fill-current" />
                <span className="ml-2 text-sm">Professionally Recommended</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Embed Options Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black">Embed AFT Calculator</h2>
              <p className="text-lg text-gray-600">
                Integrate our AFT Calculator into your website with customizable branding options
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="border border-[#FFCC01] bg-white">
                <CardHeader>
                  <CardTitle className="text-black">Clean Integration</CardTitle>
                  <CardDescription>
                    Multiple branding styles to match your website design
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">Hidden branding for commercial sites</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">Minimal attribution option</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">Elegant badge style (default)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">Subtle watermark design</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border border-[#FFCC01] bg-white">
                <CardHeader>
                  <CardTitle className="text-black">Easy Implementation</CardTitle>
                  <CardDescription>
                    Simple iframe embed with URL parameters
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-100 rounded-md p-3 mb-4">
                    <code className="text-xs text-gray-800 break-all">
                      {`<iframe src="https://aftcalculator.online/embed-aft-calculator?brand=hidden" width="100%" height="800"></iframe>`}
                    </code>
                  </div>
                  <a
                    href="/embed-examples"
                    className="inline-flex items-center px-4 py-2 bg-[#FFCC01] text-black font-medium rounded-md hover:bg-[#FFCC01]/90 transition-colors"
                  >
                    View Examples & Get Code
                  </a>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
