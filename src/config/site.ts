/**
 * Site configuration
 * Centralized configuration for domain, URLs, and site metadata
 */

export const siteConfig = {
  // Domain configuration
  domain: process.env.NEXT_PUBLIC_SITE_DOMAIN || 'aftcalculator.online',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://aftcalculator.online',

  // Site metadata
  name: 'AFT Calculator',
  title: 'AFT Calculator - Free Army Fitness Test Score Calculator',
  description: 'Calculate your Army Fitness Test score instantly with our free AFT Calculator. Get accurate results for all five events using official 2025 standards.',

  // Social metadata
  openGraph: {
    type: 'website',
    locale: 'en_US',
    siteName: 'AFT Calculator',
  },

  // Google Analytics
  analytics: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || 'G-GMGXTLR82J',
  },

  // Microsoft Clarity
  clarity: {
    clarityId: process.env.NEXT_PUBLIC_CLARITY_ID || 'rv67prxs7d',
  },

  // Google AdSense
  adsenseAccount: process.env.NEXT_PUBLIC_ADSENSE_ACCOUNT || 'ca-pub-****************',
  adsensePublisher: process.env.NEXT_PUBLIC_ADSENSE_PUBLISHER || 'pub-****************',

  // App Store
  appStore: {
    iosAppUrl: process.env.NEXT_PUBLIC_IOS_APP_URL || 'https://apps.apple.com/app/id6746973298',
  },

  // Organization info
  organization: {
    name: 'AFT Calculator Team',
    publisher: 'AFT Calculator',
  },
} as const;

// Helper functions
export const getAbsoluteUrl = (path: string = '') => {
  return `${siteConfig.url}${path}`;
};

export const getCanonicalUrl = (path: string = '') => {
  return getAbsoluteUrl(path);
};

export const getSitemapUrl = () => {
  return getAbsoluteUrl('/my/sitemap.xml');
};

export const getLogoUrl = () => {
  return getAbsoluteUrl('/logo.png');
};
