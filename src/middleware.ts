import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // 获取请求路径
  const pathname = request.nextUrl.pathname;
  
  // 如果是embed页面，允许iframe嵌入
  if (pathname.startsWith('/embed-')) {
    // 移除X-Frame-Options头，允许所有域名嵌入
    response.headers.delete('X-Frame-Options');
    
    // 设置CSP允许所有域名嵌入
    response.headers.set(
      'Content-Security-Policy',
      'frame-ancestors *;'
    );
    
    // 添加其他安全头
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  } else {
    // 其他页面禁止iframe嵌入
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了：
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
