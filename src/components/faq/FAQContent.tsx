'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Search, HelpCircle, Target, BookOpen, Shield } from 'lucide-react';

const faqCategories = [
  {
    name: 'General AFT',
    icon: HelpCircle,
    color: 'bg-blue-100 text-blue-800',
  },
  {
    name: 'Scoring',
    icon: Target,
    color: 'bg-green-100 text-green-800',
  },
  {
    name: 'Training',
    icon: BookOpen,
    color: 'bg-purple-100 text-purple-800',
  },
  {
    name: 'Safety',
    icon: Shield,
    color: 'bg-red-100 text-red-800',
  },
];

const faqData = [
  {
    category: 'General AFT',
    question: 'What is the Army Fitness Test (AFT)?',
    answer: 'The Army Fitness Test (AFT) is a comprehensive physical fitness assessment used by the U.S. Army to evaluate soldiers\' physical readiness. It consists of five events: 3 Repetition Maximum Deadlift, Hand-Release Push-Up, Sprint-Drag-Carry, Plank, and 2-Mile Run.'
  },
  {
    category: 'General AFT',
    question: 'How often do soldiers take the AFT?',
    answer: 'Soldiers typically take the AFT twice per year, with a minimum of four months between tests. However, the frequency may vary based on unit requirements and individual circumstances.'
  },
  {
    category: 'General AFT',
    question: 'What are the five AFT events?',
    answer: 'The five AFT events are: 1) 3 Repetition Maximum Deadlift (MDL), 2) Standing Power Throw (SPT), 3) Hand-Release Push-Up (HRP), 4) Sprint-Drag-Carry (SDC), 5) Leg Tuck or Plank (LTK/PLK), and 6) 2-Mile Run (2MR).'
  },
  {
    category: 'Scoring',
    question: 'How is the AFT scored?',
    answer: 'Each AFT event is scored from 0-100 points, with a minimum of 60 points required per event to pass. The total AFT score is the sum of all five event scores, with a maximum possible score of 500 points.'
  },
  {
    category: 'Scoring',
    question: 'What is a passing AFT score?',
    answer: 'To pass the AFT, a soldier must score at least 60 points in each of the five events. There is no overall minimum score requirement, but each individual event must meet the 60-point threshold.'
  },
  {
    category: 'Scoring',
    question: 'How do AFT scores affect promotion points?',
    answer: 'AFT scores contribute to promotion points for enlisted soldiers. Higher AFT scores result in more promotion points, which can help with career advancement and promotion eligibility.'
  },
  {
    category: 'Scoring',
    question: 'Are AFT standards different by age and gender?',
    answer: 'Yes, AFT scoring standards are adjusted based on age groups and gender to ensure fair assessment across all demographics. Each age group has specific performance requirements for each event.'
  },
  {
    category: 'Training',
    question: 'How should I prepare for the AFT?',
    answer: 'AFT preparation should include a well-rounded fitness program focusing on strength, endurance, and functional movement. Train specifically for each event while maintaining overall fitness through cardiovascular exercise, strength training, and flexibility work.'
  },
  {
    category: 'Training',
    question: 'How long should I train before taking the AFT?',
    answer: 'A comprehensive AFT training program typically takes 8-12 weeks to see significant improvements. However, maintaining year-round fitness is recommended rather than cramming before test dates.'
  },
  {
    category: 'Training',
    question: 'Can I retake the AFT if I fail?',
    answer: 'Yes, soldiers who fail the AFT are typically given opportunities to retake the test. The specific retake policies and timelines are determined by unit commanders and Army regulations.'
  },
  {
    category: 'Safety',
    question: 'What safety measures are in place during AFT testing?',
    answer: 'AFT testing includes trained graders, medical personnel on standby, proper equipment inspection, and adherence to safety protocols. Soldiers should report any injuries or concerns immediately.'
  },
  {
    category: 'Safety',
    question: 'What should I do if I get injured during the AFT?',
    answer: 'If you experience an injury during AFT testing, stop immediately and notify the test administrator. Seek medical attention if needed. Injured soldiers may be eligible for a retest once medically cleared.'
  },
  {
    category: 'Safety',
    question: 'Are there any medical exemptions for AFT events?',
    answer: 'Soldiers with certain medical conditions may receive temporary or permanent profiles that modify or exempt them from specific AFT events. These profiles must be issued by qualified medical personnel.'
  }
];

export default function FAQContent() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpanded = (index: number) => {
    setExpandedItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ['All', ...faqCategories.map(cat => cat.name)];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">AFT Calculator FAQ</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Find answers to common questions about the Army Fitness Test and our AFT Calculator
        </p>
        <div className="flex flex-wrap justify-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <HelpCircle className="h-3 w-3" />
            Expert Answers
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Target className="h-3 w-3" />
            Official Standards
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            Training Tips
          </Badge>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-8 space-y-4">
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex flex-wrap justify-center gap-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Category Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {faqCategories.map((category) => {
          const categoryCount = faqData.filter(faq => faq.category === category.name).length;
          return (
            <Card 
              key={category.name} 
              className="text-center cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setSelectedCategory(category.name)}
            >
              <CardContent className="pt-6">
                <category.icon className="h-8 w-8 mx-auto text-primary mb-2" />
                <h3 className="font-semibold">{category.name}</h3>
                <Badge className={category.color} variant="secondary">
                  {categoryCount} questions
                </Badge>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {filteredFAQs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <HelpCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No questions found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or category filter.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredFAQs.map((faq, index) => {
            const isExpanded = expandedItems.includes(index);
            const categoryInfo = faqCategories.find(cat => cat.name === faq.category);
            
            return (
              <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader 
                  className="pb-3"
                  onClick={() => toggleExpanded(index)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={categoryInfo?.color} variant="secondary">
                          {faq.category}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg text-left">{faq.question}</CardTitle>
                    </div>
                    <Button variant="ghost" size="sm">
                      {isExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                {isExpanded && (
                  <CardContent className="pt-0">
                    <p className="text-muted-foreground leading-relaxed">
                      {faq.answer}
                    </p>
                  </CardContent>
                )}
              </Card>
            );
          })
        )}
      </div>

      {/* Contact Section */}
      <Card className="mt-12">
        <CardContent>
            <div>
              <h3 className="font-semibold mb-2">Important Disclaimer</h3>
              <p className="text-sm text-muted-foreground leading-relaxed w-full">
                This FAQ provides general information only. Always consult official Army 
                regulations and qualified personnel for authoritative guidance on AFT 
                requirements and procedures.
              </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
