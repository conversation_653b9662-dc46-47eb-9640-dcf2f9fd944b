import type React from "react";
import { cn } from "@/lib/utils";
import { GoogleAdSenseUnit } from "./google-adsense-unit";

export interface AdContainerProps {
  /** ad unit id */
  adUnitId: string;
  /** ad size preset */
  size?:
    | "banner"
    | "leaderboard"
    | "large-leaderboard"
    | "large-rectangle"
    | "medium-rectangle"
    | "square"
    | "vertical"
    | "large-vertical"
    | "mobile-banner"
    | "custom";
  /** custom width (only when size="custom") */
  width?: number | string;
  /** custom height (only when size="custom") */
  height?: number | string;
  /** whether to enable responsive ad */
  responsive?: boolean;
  /** ad position */
  position?: "in-article" | "in-feed" | "matched-content";
  /** custom class name */
  className?: string;
  /** ad class name */
  adClassName?: string;
  /** ad container style */
  containerStyle?: "card" | "inline" | "floating" | "sticky" | "none";
  /** ad label text */
  adLabel?: string;
  /** children */
  children?: React.ReactNode;
}

/**
 * ad unit container component
 *
 * provide multiple preset size and style options for ad unit
 */
export function AdUnitContainer({
  adUnitId,
  size = "leaderboard",
  width,
  height,
  responsive = true,
  position,
  className,
  adClassName,
  containerStyle = "inline",
  adLabel = "Ad",
  children,
}: AdContainerProps) {
  // preset size config
  const sizeConfig = {
    banner: { width: 468, height: 60 },
    leaderboard: { width: 728, height: 90 },
    "large-leaderboard": { width: 970, height: 90 },
    "large-rectangle": { width: 336, height: 280 },
    "medium-rectangle": { width: 300, height: 250 },
    square: { width: 250, height: 250 },
    vertical: { width: 160, height: 600 },
    "large-vertical": { width: 300, height: 600 },
    "mobile-banner": { width: 300, height: 50 },
    custom: { width: width || "100%", height: height || "auto" },
  };

  // get current size config
  const currentSize = sizeConfig[size];
  console.log(currentSize);

  // container style config
  const containerStyles = {
    card: "rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden",
    inline: "my-4",
    floating:
      "fixed bottom-4 right-4 z-50 shadow-lg rounded-lg overflow-hidden",
    sticky: "sticky top-4 z-40",
    none: "",
  };
  return (
    <div
      className={cn(
        "ad-container relative",
        containerStyles[containerStyle],
        className
      )}
    >
      {adLabel && containerStyle !== "none" && (
        <div className="ad-label text-xs text-muted-foreground px-2 py-1 bg-muted/50">
          {adLabel}
        </div>
      )}

      <GoogleAdSenseUnit
        adUnitId={adUnitId}
        publisherId={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CODE}
        width={responsive ? "100%" : currentSize.width}
        height={currentSize.height}
        responsive={responsive}
        position={position}
        fallback={children}
        className={adClassName}
      />
    </div>
  );
}