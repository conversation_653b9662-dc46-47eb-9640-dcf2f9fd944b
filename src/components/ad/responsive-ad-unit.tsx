import type React from "react";
import { cn } from "@/lib/utils";
import { AdUnitContainer } from "./ad-unit-container";

export interface ResponsiveAdUnitProps {
  /** Desktop ad unit id */
  desktopAdUnitId: string;
  /** Mobile ad unit id */
  mobileAdUnitId: string;
  /** Desktop ad size */
  desktopSize?: "banner" | "leaderboard" | "large-leaderboard" | "large-rectangle" | "medium-rectangle" | "square" | "vertical" | "large-vertical";
  /** Mobile ad size */
  mobileSize?: "mobile-banner" | "medium-rectangle" | "square";
  /** Container style */
  containerStyle?: "card" | "inline" | "floating" | "sticky" | "none";
  /** Custom class name */
  className?: string;
  /** Ad class name */
  adClassName?: string;
  /** Ad label text */
  adLabel?: string;
  /** Fallback content */
  children?: React.ReactNode;
  /** Whether to enable responsive ad */
  responsive?: boolean;
}

/**
 * Responsive Ad Unit Component
 * 
 * Automatically displays appropriate ad sizes for different screen sizes:
 * - Desktop: Leaderboard (728x90), Large Rectangle (336x280), etc.
 * - Tablet: Medium Rectangle (300x250), Banner (468x60)
 * - Mobile: Mobile Banner (300x50), Medium Rectangle (300x250)
 */
export function ResponsiveAdUnit({
  desktopAdUnitId,
  mobileAdUnitId,
  desktopSize = "leaderboard",
  mobileSize = "mobile-banner",
  containerStyle = "none",
  className,
  adClassName,
  adLabel,
  children,
  responsive = false,
}: ResponsiveAdUnitProps) {
  return (
    <div className={cn("responsive-ad-unit", className)}>
      {/* Desktop Ad */}
      <div className="hidden md:block">
        <AdUnitContainer
          adUnitId={desktopAdUnitId}
          size={desktopSize}
          containerStyle={containerStyle}
          responsive={responsive}
          className="w-full mx-auto"
          adClassName={adClassName}
          adLabel={adLabel}
        >
          {children}
        </AdUnitContainer>
      </div>
      
      {/* Mobile Ad */}
      <div className="block md:hidden">
        <AdUnitContainer
          adUnitId={mobileAdUnitId}
          size={mobileSize}
          containerStyle={containerStyle}
          responsive={responsive}
          className="w-full mx-auto"
          adClassName={adClassName}
          adLabel={adLabel}
        >
          {children}
        </AdUnitContainer>
      </div>
    </div>
  );
}

/**
 * Predefined responsive ad configurations for common use cases
 */
export const ResponsiveAdConfigs = {
  // Top banner - Leaderboard on desktop, mobile banner on mobile
  topBanner: {
    desktopSize: "leaderboard" as const,
    mobileSize: "mobile-banner" as const,
    containerStyle: "none" as const,
  },
  
  // Content ad - Large rectangle on desktop, medium rectangle on mobile
  content: {
    desktopSize: "large-rectangle" as const,
    mobileSize: "medium-rectangle" as const,
    containerStyle: "card" as const,
  },
  
  // Sidebar ad - Large rectangle on desktop, medium rectangle on mobile
  sidebar: {
    desktopSize: "large-rectangle" as const,
    mobileSize: "medium-rectangle" as const,
    containerStyle: "card" as const,
  },
  
  // Bottom banner - Leaderboard on desktop, mobile banner on mobile
  bottomBanner: {
    desktopSize: "leaderboard" as const,
    mobileSize: "mobile-banner" as const,
    containerStyle: "none" as const,
  },
} as const;

/**
 * Quick responsive ad component with predefined configurations
 */
interface QuickResponsiveAdProps {
  /** Ad configuration type */
  type: keyof typeof ResponsiveAdConfigs;
  /** Desktop ad unit id */
  desktopAdUnitId: string;
  /** Mobile ad unit id */
  mobileAdUnitId: string;
  /** Custom class name */
  className?: string;
  /** Ad class name */
  adClassName?: string;
  /** Ad label text */
  adLabel?: string;
  /** Fallback content */
  children?: React.ReactNode;
}

export function QuickResponsiveAd({
  type,
  desktopAdUnitId,
  mobileAdUnitId,
  className,
  adClassName,
  adLabel,
  children,
}: QuickResponsiveAdProps) {
  const config = ResponsiveAdConfigs[type];
  
  return (
    <ResponsiveAdUnit
      desktopAdUnitId={desktopAdUnitId}
      mobileAdUnitId={mobileAdUnitId}
      desktopSize={config.desktopSize}
      mobileSize={config.mobileSize}
      containerStyle={config.containerStyle}
      className={className}
      adClassName={adClassName}
      adLabel={adLabel}
    >
      {children}
    </ResponsiveAdUnit>
  );
}
