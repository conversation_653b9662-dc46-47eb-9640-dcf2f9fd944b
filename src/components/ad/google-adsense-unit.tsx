"use client";

import type React from "react";

import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";

export interface GoogleAdSenseProps {
  /** ad unit id */
  adUnitId: string;
  /** publisher id */
  publisherId: string;
  /** ad width */
  width?: number | string;
  /** ad height */
  height?: number | string;
  /** ad format */
  format?: "auto" | "rectangle" | "horizontal" | "vertical";
  /** whether to enable responsive ad */
  responsive?: boolean;
  /** ad position */
  position?: "in-article" | "in-feed" | "matched-content";
  /** custom class name */
  className?: string;
  /** content to display before ad loads */
  fallback?: React.ReactNode;
  /** ad load timeout (milliseconds) */
  timeout?: number;
}

/**
 * Google AdSense Unit
 *
 */
export function GoogleAdSenseUnit({
  adUnitId,
  publisherId,
  width,
  height,
  format = "auto",
  responsive = true,
  position,
  className,
  fallback,
  timeout = 3000,
}: GoogleAdSenseProps) {
  const adContainerRef = useRef<HTMLDivElement>(null);
  const adLoadedRef = useRef<boolean>(false);

  useEffect(() => {
    // check if Google AdSense script is loaded
    const isScriptExist = document.querySelector(
      'script[src*="pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"]'
    );

    if (!isScriptExist) {
      // load Google AdSense script
      const script = document.createElement("script");
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${publisherId}`;
      script.async = true;
      script.crossOrigin = "anonymous";
      document.head.appendChild(script);
    }

    // create ad unit
    const createAd = () => {
      if (adContainerRef.current && !adLoadedRef.current) {
        try {
          const adElement = document.createElement("ins");
          adElement.className = "adsbygoogle";
          adElement.style.display = "block";
          adElement.setAttribute("data-ad-client", publisherId);
          adElement.setAttribute("data-ad-slot", adUnitId);

          if (responsive) {
            adElement.setAttribute("data-ad-format", format);
            adElement.setAttribute("data-full-width-responsive", "true");
          } else {
            if (width)
              adElement.style.width =
                typeof width === "number" ? `${width}px` : width;
            if (height)
              adElement.style.height =
                typeof height === "number" ? `${height}px` : height;
          }

          if (position) {
            adElement.setAttribute("data-ad-layout", position);
          }

          adContainerRef.current.innerHTML = "";
          adContainerRef.current.appendChild(adElement);

          // execute ad load
          (window.adsbygoogle = window.adsbygoogle || []).push({});
          adLoadedRef.current = true;
        } catch (error) {
          console.error("Error loading AdSense ad:", error);
        }
      }
    };

    // set timeout, if ad not loaded, display fallback
    const timeoutId = setTimeout(() => {
      if (adContainerRef.current && !adLoadedRef.current && fallback) {
        adContainerRef.current.innerHTML = "";
        const fallbackContainer = document.createElement("div");
        adContainerRef.current.appendChild(fallbackContainer);
      }
    }, timeout);

    // when AdSense script is loaded, create ad
    if (isScriptExist) {
      createAd();
    } else {
      window.addEventListener("load", createAd);
    }

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("load", createAd);
    };
  }, [
    adUnitId,
    publisherId,
    width,
    height,
    format,
    responsive,
    position,
    fallback,
    timeout,
  ]);

  return (
    <div
      ref={adContainerRef}
      className={cn(
        "google-adsense-unit",
        responsive ? "w-full overflow-hidden text-center" : "",
        className
      )}
      style={{
        minHeight: height || (responsive ? "100px" : "auto"),
        width: width || (responsive ? "100%" : "auto"),
      }}
    >
      {fallback}
    </div>
  );
}

// 为了类型安全，声明全局 adsbygoogle 变量
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
