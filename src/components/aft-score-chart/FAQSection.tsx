import React from 'react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: "What is the AFT Score Chart?",
    answer: "The AFT Score Chart provides a standardized guide for calculating physical fitness scores based on specific event performances, including deadlift, push-ups, sprint-drag-carry, plank, and the 2-mile run."
  },
  {
    question: "How is the AFT score calculated?",
    answer: "AFT scores are calculated by adding the points earned in each physical event, with a maximum possible score of 300."
  },
  {
    question: "Are the 2025 AFT standards different from previous years?",
    answer: "Yes, the 2025 standards include updated scoring tables and performance benchmarks to reflect current military fitness requirements."
  },
  {
    question: "Why is the AFT score important?",
    answer: "A higher AFT score not only demonstrates physical readiness but also contributes to career advancement and promotion points."
  },
  {
    question: "What are the five AFT events?",
    answer: "The five AFT events are: Maximum Deadlift (MDL), Hand Release Push-up (HRP), Sprint-Drag-Carry (SDC), Plank (PLK), and Two-Mile Run (2MR)."
  },
  {
    question: "How often should I take the AFT?",
    answer: "The AFT is typically administered twice per year, but frequency may vary based on unit requirements and individual circumstances."
  }
];

export default function FAQSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions (FAQ)
            </h2>
            <p className="text-lg text-gray-600">
              Common questions about the AFT Score Chart and Army Fitness Test standards
            </p>
          </div>

          {/* FAQ Items */}
          <div className="space-y-8">
            {faqItems.map((item, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {index + 1}. {item.question}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {item.answer}
                </p>
              </div>
            ))}
          </div>

          {/* Additional Info */}
          {/* <div className="mt-12 p-6 bg-[#FFCC01]/10 rounded-lg border border-[#FFCC01]/20">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Need More Information?
            </h3>
            <p className="text-gray-700 mb-4">
              For the most up-to-date AFT standards and detailed scoring information, 
              consult your unit's fitness coordinator or visit the official Army fitness resources.
            </p>
            <div className="flex flex-wrap gap-4">
              <a
                href="/"
                className="inline-flex items-center px-4 py-2 bg-[#FFCC01] text-black font-medium rounded-md hover:bg-[#FFCC01]/90 transition-colors"
              >
                Use AFT Calculator
              </a>
              <a
                href="/training"
                className="inline-flex items-center px-4 py-2 border border-[#FFCC01] text-[#FFCC01] font-medium rounded-md hover:bg-[#FFCC01]/10 transition-colors"
              >
                Training Guide
              </a>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
}
