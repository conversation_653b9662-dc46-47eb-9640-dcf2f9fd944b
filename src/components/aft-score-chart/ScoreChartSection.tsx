'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import ImageModal from '@/components/ui/image-modal';

interface ChartItem {
  id: string;
  title: string;
  subtitle: string;
  images: string[];
  description?: string;
}

const chartItems: ChartItem[] = [
  {
    id: 'mdl',
    title: 'MDL',
    subtitle: '3 REPETITION MAXIMUM DEADLIFT',
    images: ['/mdl.webp'],
    description: 'The Maximum Deadlift (MDL) measures muscular strength of the back, glutes, and legs.'
  },
  {
    id: 'hrp',
    title: 'HRP',
    subtitle: 'HAND RELEASE PUSH-UP',
    images: ['/hrp.webp'],
    description: 'The Hand Release Push-up (HRP) measures muscular endurance of the chest, shoulders, and triceps.'
  },
  {
    id: 'sdc',
    title: 'SDC',
    subtitle: 'SPRINT-DRAG-CARRY',
    images: ['/sdc1.webp', '/sdc2.webp'],
    description: 'The Sprint-Drag-Carry (SDC) measures anaerobic capacity and muscular strength, endurance, and power.'
  },
  {
    id: 'plk',
    title: 'PLK',
    subtitle: 'PLANK',
    images: ['/plk1.webp', '/plk2.webp'],
    description: 'The Plank (PLK) measures muscular endurance of the trunk stabilizer muscles.'
  },
  {
    id: '2mr',
    title: '2MR',
    subtitle: 'TWO-MILE RUN',
    images: ['/2mr1-n.webp', '/2mr2.webp'],
    description: 'The Two-Mile Run (2MR) measures aerobic and muscular endurance.'
  }
];

export default function ScoreChartSection() {
  const [modalImage, setModalImage] = useState<{src: string, alt: string, title: string} | null>(null);

  const openModal = (src: string, alt: string, title: string) => {
    setModalImage({ src, alt, title });
  };

  const closeModal = () => {
    setModalImage(null);
  };

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="space-y-16">
          {chartItems.map((item, index) => (
            <div key={item.id} className="max-w-6xl mx-auto">
              {/* Section Header */}
              <div className="text-center mb-8">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {item.title} - {item.subtitle}
                </h2>
                {item.description && (
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                    {item.description}
                  </p>
                )}
              </div>

              {/* Chart Images */}
              <div className="grid gap-8 md:gap-12">
                {item.images.length === 1 ? (
                  <div className="flex justify-center">
                    <div className="relative w-full max-w-4xl cursor-pointer group">
                      <Image
                        src={item.images[0]}
                        alt={`${item.title} chart`}
                        width={800}
                        height={600}
                        className="w-full h-auto rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 group-hover:scale-[1.02]"
                        priority={index < 1}
                        loading={index < 1 ? "eager" : "lazy"}
                        quality={85}
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                        onClick={() => openModal(item.images[0], `${item.title} chart`, `${item.title} - ${item.subtitle}`)}
                      />
                      {/* <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-lg transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 px-4 py-2 rounded-lg">
                          <span className="text-sm font-medium text-gray-900">点击查看大图</span>
                        </div>
                      </div> */}
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {item.images.map((image, imgIndex) => (
                      <div key={imgIndex} className="relative group cursor-pointer">
                        <Image
                          src={image}
                          alt={`${item.title} chart ${imgIndex + 1}`}
                          width={600}
                          height={400}
                          className="w-full h-auto rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 group-hover:scale-[1.02]"
                          priority={index < 1 && imgIndex < 1}
                          loading={index < 1 && imgIndex < 1 ? "eager" : "lazy"}
                          quality={85}
                          placeholder="blur"
                          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                          onClick={() => openModal(image, `${item.title} chart ${imgIndex + 1}`, `${item.title} - ${item.subtitle} (${imgIndex + 1})`)}
                        />
                        {/* <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-lg transition-all duration-300 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 px-4 py-2 rounded-lg">
                            <span className="text-sm font-medium text-gray-900">点击查看大图</span>
                          </div>
                        </div> */}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Separator */}
              {index < chartItems.length - 1 && (
                <div className="mt-16 flex items-center justify-center">
                  <div className="flex-1 border-b border-gray-200"></div>
                  <div className="px-4">
                    <div className="w-3 h-3 bg-[#FFCC01] rounded-full"></div>
                  </div>
                  <div className="flex-1 border-b border-gray-200"></div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Image Modal */}
      {modalImage && (
        <ImageModal
          isOpen={!!modalImage}
          onClose={closeModal}
          src={modalImage.src}
          alt={modalImage.alt}
          title={modalImage.title}
        />
      )}
    </section>
  );
}
