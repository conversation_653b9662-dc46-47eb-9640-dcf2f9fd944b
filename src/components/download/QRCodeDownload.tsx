'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { QrCode, Apple } from 'lucide-react';
import QRCode from 'react-qr-code';

interface QRCodeDownloadProps {
  appStoreUrl: string;
}

export default function QRCodeDownload({ appStoreUrl }: QRCodeDownloadProps) {
  const handleAppStoreClick = () => {
    window.open(appStoreUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="flex flex-col md:flex-row items-center justify-center gap-8 mb-12">
      {/* App Store Button */}
      <div className="flex flex-col items-center space-y-4">
        <Button
          onClick={handleAppStoreClick}
          className="bg-black hover:bg-gray-800 text-white px-8 py-6 text-lg rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl"
          size="lg"
        >
          <Apple className="w-6 h-6 mr-3" />
          Download on the App Store
        </Button>
        <p className="text-sm text-gray-500">Compatible with iPhone and iPad</p>
      </div>

      {/* QR Code */}
      <div className="flex flex-col items-center space-y-4">
        <div className="bg-white p-4 rounded-xl shadow-lg border-2 border-[#FFCC01]/20">
          <QRCode
            value={appStoreUrl}
            size={120}
            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
            viewBox={`0 0 120 120`}
          />
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <QrCode className="w-4 h-4 mr-1 text-[#FFCC01]" />
            <span className="text-sm font-medium text-gray-700">Scan to Download</span>
          </div>
          <p className="text-xs text-gray-500">Use camera to scan QR code</p>
        </div>
      </div>
    </div>
  );
}
