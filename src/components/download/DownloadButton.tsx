'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface DownloadButtonProps {
  appStoreUrl: string;
}

export default function DownloadButton({ appStoreUrl }: DownloadButtonProps) {
  const handleAppStoreClick = () => {
    window.open(appStoreUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <Button
      onClick={handleAppStoreClick}
      className="bg-[#FFCC01] hover:bg-[#FFCC01]/90 text-black px-8 py-6 text-lg rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl font-semibold"
      size="lg"
    >
      <Download className="w-6 h-6 mr-3" />
      Download App Now
    </Button>
  );
}
