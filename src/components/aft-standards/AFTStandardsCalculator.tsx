'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Target } from 'lucide-react';
import { getAFTStandardsByAge } from '@/lib/aft-standards';
import {
  DeadliftIcon,
  PushUpIcon,
  SprintIcon,
  PlankIcon,
  RunIcon
} from '@/components/ui/icons';

interface AFTStandards {
  ageGroup: number;
  gender: 'Male' | 'Female';
  standards: {
    deadlift: { min: number; max: number };
    pushups: { min: number; max: number };
    sprintDragCarry: { min: string; max: string };
    plank: { min: string; max: string };
    twoMileRun: { min: string; max: string };
  };
}

export default function AFTStandardsCalculator() {
  const [age, setAge] = useState<string>('');
  const [gender, setGender] = useState<'Male' | 'Female' | ''>('');
  const [standards, setStandards] = useState<AFTStandards | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [isEmbedded, setIsEmbedded] = useState(false);
  const [brandStyle, setBrandStyle] = useState<'hidden' | 'minimal' | 'badge' | 'subtle'>('badge');

  // 检测是否在embed环境中以及品牌标识样式
  useEffect(() => {
    const isEmbed = window.location.pathname.startsWith('/embed-');
    setIsEmbedded(isEmbed);

    // 从URL参数获取品牌样式设置
    if (isEmbed) {
      const urlParams = new URLSearchParams(window.location.search);
      const style = urlParams.get('brand') as 'hidden' | 'minimal' | 'badge' | 'subtle';
      setBrandStyle(style || 'badge');
    }
  }, []);

  const handleCalculate = () => {
    if (!age || !gender) {
      alert('Please enter both age and gender');
      return;
    }

    const ageNum = parseInt(age);
    if (ageNum < 17 || ageNum > 100) {
      alert('Age must be between 17 and 100');
      return;
    }

    const result = getAFTStandardsByAge(ageNum, gender);
    setStandards(result);
    setShowResults(true);
  };

  const handleReset = () => {
    setAge('');
    setGender('');
    setStandards(null);
    setShowResults(false);
  };

  return (
    <div className="max-w-6xl mx-auto relative">
      {/* Main Card - Input and Results */}
      <Card className="relative">

        <CardContent className="pt-6">
          {/* Input Section */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-4 max-w-3xl mx-auto mb-6">
            <div className="space-y-2">
              <Label htmlFor="age">Age</Label>
              <Input
                id="age"
                type="number"
                placeholder="e.g., 25"
                value={age}
                onChange={(e) => setAge(e.target.value)}
                min="17"
                max="100"
                className="touch-manipulation"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select value={gender} onValueChange={(value: 'Male' | 'Female') => setGender(value)}>
                <SelectTrigger className="touch-manipulation">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Male">M/C</SelectItem>
                  <SelectItem value="Female">F</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end sm:col-span-1">
              <Button
                onClick={handleCalculate}
                className="w-full bg-[#FFCC01] hover:bg-[#FFCC01]/90 text-black font-semibold touch-manipulation min-h-[44px]"
              >
                <Target className="h-4 w-4 mr-2" />
                Show Standards
              </Button>
            </div>
            <div className="flex items-end sm:col-span-1">
              <Button
                onClick={handleReset}
                variant="outline"
                className="w-full touch-manipulation min-h-[44px]"
              >
                Reset
              </Button>
            </div>
          </div>

          {/* Results Table */}
          {showResults && standards && (
            <div className="border border-[#FFCC01] bg-white rounded-lg overflow-hidden mobile-table-scroll">
              {/* Table Header */}
              <div className="bg-gray-50 border-b border-[#FFCC01]/20">
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center min-w-[320px] relative">
                  <div className="w-8 sm:w-12"></div> {/* Space for icon */}
                  <div className="font-bold text-gray-700 text-xs sm:text-sm flex items-center gap-2">
                    Event
                    {/* 嵌入式页面的品牌标识 - 与Event标题同行 */}
                    {isEmbedded && brandStyle !== 'hidden' && (
                      <div className="ml-2">
                        {brandStyle === 'minimal' && (
                          <a
                            href="https://aftcalculator.online"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[8px] text-gray-400 hover:text-gray-600 transition-colors opacity-50 hover:opacity-70"
                            title="Powered by AFT Calculator"
                          >
                            aftcalculator.online
                          </a>
                        )}

                        {brandStyle === 'badge' && (
                          <a
                            href="https://aftcalculator.online"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-[#FFCC01]/10 hover:to-[#FFCC01]/5 rounded border border-gray-200 hover:border-[#FFCC01]/30 transition-all duration-300"
                            title="Powered by AFT Calculator"
                          >
                            <svg className="w-2 h-2 text-[#FFCC01]" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                            </svg>
                            <span className="text-[8px] font-medium text-gray-600 hover:text-gray-800">
                              AFT
                            </span>
                          </a>
                        )}

                        {brandStyle === 'subtle' && (
                          <div className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-white/50 rounded-full border border-gray-100">
                            <div className="w-1 h-1 bg-[#FFCC01] rounded-full opacity-60"></div>
                            <a
                              href="https://aftcalculator.online"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[8px] text-gray-500 hover:text-gray-700 transition-colors font-light"
                              title="Powered by AFT Calculator"
                            >
                              AFT
                            </a>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="font-bold text-gray-700 text-center text-xs sm:text-sm">Min. (60 Pts)</div>
                  <div className="font-bold text-gray-700 text-center text-xs sm:text-sm">Max. (100 Pts)</div>
                </div>
              </div>

              {/* Table Rows */}
              <div className="divide-y divide-gray-200 min-w-[320px]">
                {/* MDL - 3 Repetition Maximum Deadlift */}
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center hover:bg-gray-50 transition-colors">
                  <div className="flex justify-center">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
                      <DeadliftIcon className="w-5 h-5 sm:w-8 sm:h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm sm:text-base text-black">MDL</h3>
                    <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide hidden sm:block">3 REPETITION MAXIMUM DEADLIFT</p>
                    <p className="text-xs text-gray-600 sm:hidden">Deadlift</p>
                  </div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.deadlift.min} lbs</div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.deadlift.max} lbs</div>
                </div>

                {/* HRP - Hand Release Push-Up */}
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center hover:bg-gray-50 transition-colors">
                  <div className="flex justify-center">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
                      <PushUpIcon className="w-5 h-5 sm:w-8 sm:h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm sm:text-base text-black">HRP</h3>
                    <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide hidden sm:block">HAND RELEASE PUSH-UP - ARM EXTENSION</p>
                    <p className="text-xs text-gray-600 sm:hidden">Push-Up</p>
                  </div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.pushups.min} reps</div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.pushups.max} reps</div>
                </div>

                {/* SDC - Sprint-Drag-Carry */}
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center hover:bg-gray-50 transition-colors">
                  <div className="flex justify-center">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
                      <SprintIcon className="w-5 h-5 sm:w-8 sm:h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm sm:text-base text-black">SDC</h3>
                    <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide hidden sm:block">SPRINT-DRAG-CARRY</p>
                    <p className="text-xs text-gray-600 sm:hidden">Sprint-Drag</p>
                  </div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.sprintDragCarry.min}</div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.sprintDragCarry.max}</div>
                </div>

                {/* PLK - Plank */}
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center hover:bg-gray-50 transition-colors">
                  <div className="flex justify-center">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
                      <PlankIcon className="w-5 h-5 sm:w-8 sm:h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm sm:text-base text-black">PLK</h3>
                    <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide hidden sm:block">PLANK</p>
                    <p className="text-xs text-gray-600 sm:hidden">Plank</p>
                  </div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.plank.min}</div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.plank.max}</div>
                </div>

                {/* 2MR - Two Mile Run */}
                <div className="grid grid-cols-4 gap-2 sm:gap-4 p-3 sm:p-4 items-center hover:bg-gray-50 transition-colors">
                  <div className="flex justify-center">
                    <div className="w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
                      <RunIcon className="w-5 h-5 sm:w-8 sm:h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm sm:text-base text-black">2MR</h3>
                    <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide hidden sm:block">TWO-MILE RUN</p>
                    <p className="text-xs text-gray-600 sm:hidden">2-Mile Run</p>
                  </div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.twoMileRun.min}</div>
                  <div className="text-center font-bold text-xs sm:text-sm text-black">{standards.standards.twoMileRun.max}</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
