'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

import { Slider } from '@/components/ui/slider';
import { RadarChart } from '@/components/ui/radar-chart';
import {
  DeadliftIcon,
  PushUpIcon,
  SprintIcon,
  PlankIcon,
  RunIcon
} from '@/components/ui/icons';
import { AFTInput } from '@/lib/aft-calculations';
import { calculateMDLScore } from '@/lib/aft-scoring/mdl-scoring';
import { calculateHRPScore } from '@/lib/aft-scoring/hrp-scoring';
import { calculateSDCScore } from '@/lib/aft-scoring/sdc-scoring';
import { calculatePLKScore } from '@/lib/aft-scoring/plk-scoring';
import { calculate2MRScore } from '@/lib/aft-scoring/tmr-scoring';
import {
  getHRPMinimumRequirements,
  getHRPMaximumRequirements,
  getMDLMinimumRequirements,
  getMDLMaximumRequirements,
  getSDCMinimumRequirements,
  getSDCMaximumRequirements,
  getPLKMinimumRequirements,
  getPLKMaximumRequirements,
  get2MRMinimumRequirements,
  get2MRMaximumRequirements
} from '@/data/aft';



// 输入组件 - 支持滑动条和手动输入
interface InputSliderProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step: number;
  unit: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  formatDisplay?: (value: number) => string;
  getScore: (value: number) => number;
  isTimeEvent?: boolean; // 标识是否为时间类项目（SDC、2MR）
}

function InputSlider({
  value,
  onChange,
  min,
  max,
  step,
  unit,
  label,
  description,
  icon,
  formatDisplay,
  getScore,
  isTimeEvent = false // 新增参数，标识是否为时间类项目（SDC、2MR）
}: InputSliderProps) {
  const [inputValue, setInputValue] = useState(value.toString());
  const [isEditing, setIsEditing] = useState(false);

  // 同步外部value到内部inputValue
  useEffect(() => {
    if (!isEditing) {
      setInputValue(value.toString());
    }
  }, [value, isEditing]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputBlur = () => {
    setIsEditing(false);
    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      // 如果超出边界值，限制在边界值范围内
      const clampedValue = Math.max(min, Math.min(max, Math.round(numValue)));
      onChange(clampedValue);
      setInputValue(clampedValue.toString());
    } else {
      setInputValue(value.toString());
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setInputValue(value.toString());
    }
  };

  const displayValue = formatDisplay ? formatDisplay(value) : `${value}${unit}`;

  // 对于时间类项目，需要反向滑动条逻辑（时间越短，进度条越满）
  const sliderValue = isTimeEvent ? max + min - value : value;
  const handleSliderChange = (newValue: number[]) => {
    const actualValue = isTimeEvent ? max + min - newValue[0] : newValue[0];
    onChange(actualValue);
  };

  return (
    <Card className="border border-[#FFCC01] bg-white p-3 sm:p-4 rounded-lg">
      <CardContent className="p-0">
        <div className="flex flex-col gap-3">
          {/* 第一行：图标、项目名称、输入框、得分在同一行 */}
          <div className="flex items-center gap-3 sm:gap-4">
            <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full border-2 border-[#FFCC01] bg-white flex items-center justify-center flex-shrink-0">
              {icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-sm sm:text-base text-black">{label}</h3>
              <p className="text-xs sm:text-sm text-gray-600 uppercase tracking-wide">{description}</p>
            </div>
            {/* 输入框区域 */}
            <div className="flex-shrink-0">
              {isEditing ? (
                <Input
                  value={inputValue}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  onKeyDown={handleInputKeyDown}
                  onFocus={() => setIsEditing(true)}
                  className="w-24 sm:w-28 h-10 text-base sm:text-lg font-bold text-center border-2 border-[#FFCC01] focus:border-[#FFCC01] focus:ring-2 focus:ring-[#FFCC01] focus:ring-opacity-50 rounded-md bg-yellow-50"
                  autoFocus
                />
              ) : (
                <div
                  className="relative inline-block cursor-pointer group touch-manipulation"
                  onClick={() => setIsEditing(true)}
                >
                  <span className="font-bold text-base sm:text-lg px-2 sm:px-3 py-2 rounded-md border-2 border-dashed border-[#FFCC01] bg-yellow-50 hover:bg-yellow-100 transition-colors duration-200 inline-flex items-center gap-1">
                    {displayValue}
                    <svg className="w-3 h-3 sm:w-4 sm:h-4 text-[#FFCC01] opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </span>
                </div>
              )}
            </div>
            {/* 分数显示区域 */}
            <div className="text-right min-w-[70px] sm:min-w-[80px] flex-shrink-0">
              <span className={`font-bold text-xl sm:text-2xl ${getScore(value) < 60 ? 'text-[#FF6B6B]' : 'text-black'}`}>
                {getScore(value)}
              </span>
              <span className="text-xs sm:text-sm text-gray-600 ml-1">points</span>
            </div>
          </div>

          {/* 第二行：进度条单独一行，左对齐 */}
          <div className="ml-0">
            <Slider
              value={[sliderValue]}
              onValueChange={handleSliderChange}
              min={min}
              max={max}
              step={step}
              className="w-full touch-manipulation"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function AFTCalculator() {
  const [input, setInput] = useState<AFTInput>({
    age: 17, // 对应17-21年龄组
    gender: 'male',
    deadlift: 170, // 接近截图中的值
    pushUp: 20, // 接近截图中的值
    sprintDragCarry: 130, // 2分10秒
    plank: 120, // 2分0秒
    twoMileRun: 1150, // 19分10秒
  });

  const [isClient, setIsClient] = useState(false);

  // 检测是否在embed环境中以及品牌标识样式
  const [isEmbedded, setIsEmbedded] = useState(false);
  const [brandStyle, setBrandStyle] = useState<'hidden' | 'minimal' | 'badge' | 'subtle'>('badge');

  useEffect(() => {
    setIsClient(true);
    const isEmbed = window.location.pathname.startsWith('/embed-');
    setIsEmbedded(isEmbed);

    // 从URL参数获取品牌样式设置
    if (isEmbed) {
      const urlParams = new URLSearchParams(window.location.search);
      const style = urlParams.get('brand') as 'hidden' | 'minimal' | 'badge' | 'subtle';
      setBrandStyle(style || 'badge');
    }
  }, []);


  // 年龄组辅助函数
  const getAgeGroupDisplay = (age: number): string => {
    if (age <= 21) return "17-21";
    if (age <= 26) return "22-26";
    if (age <= 31) return "27-31";
    if (age <= 36) return "32-36";
    if (age <= 41) return "37-41";
    if (age <= 46) return "42-46";
    if (age <= 51) return "47-51";
    if (age <= 56) return "52-56";
    if (age <= 61) return "57-61";
    return "62+";
  };

  const getAgeFromGroup = (group: string): number => {
    switch (group) {
      case "17-21": return 17;
      case "22-26": return 22;
      case "27-31": return 27;
      case "32-36": return 32;
      case "37-41": return 37;
      case "42-46": return 42;
      case "47-51": return 47;
      case "52-56": return 52;
      case "57-61": return 57;
      case "62+": return 62;
      default: return 17;
    }
  };

  // 根据gender/age动态计算各项测试的范围 - 使用useMemo优化性能
  const testRanges = useMemo(() => {
    try {
      // 获取各项目的最小和最大要求
      const hrpMin = getHRPMinimumRequirements(input.age, input.gender);
      const hrpMax = getHRPMaximumRequirements(input.age, input.gender);
      const mdlMin = getMDLMinimumRequirements(input.age, input.gender);
      const mdlMax = getMDLMaximumRequirements(input.age, input.gender);
      const sdcMin = getSDCMinimumRequirements(input.age, input.gender);
      const sdcMax = getSDCMaximumRequirements(input.age, input.gender);
      const plkMin = getPLKMinimumRequirements(input.age, input.gender);
      const plkMax = getPLKMaximumRequirements(input.age, input.gender);
      const tmrMin = get2MRMinimumRequirements(input.age, input.gender);
      const tmrMax = get2MRMaximumRequirements(input.age, input.gender);

      return {
        // MDL: 根据当前gender/age的实际数据范围
        deadlift: {
          min: mdlMin.minValue || 70,
          max: mdlMax.maxValue || 350,
          step: 1,
          unit: 'lbs'
        },
        // HRP: 根据当前gender/age的实际数据范围
        pushUp: {
          min: hrpMin.minValue || 5,
          max: hrpMax.maxValue || 62,
          step: 1,
          unit: 'reps'
        },
        // SDC: 根据当前gender/age的实际数据范围
        sprintDragCarry: {
          min: sdcMax.maxValue || 89,
          max: sdcMin.minValue || 347,
          step: 1,
          unit: 'sec'
        },
        // PLK: 根据当前gender/age的实际数据范围
        plank: {
          min: plkMin.minValue || 41,
          max: plkMax.maxValue || 220,
          step: 1,
          unit: 'sec'
        },
        // 2MR: 根据当前gender/age的实际数据范围
        twoMileRun: {
          min: tmrMax.maxValue || 802,
          max: tmrMin.minValue || 1672,
          step: 1,
          unit: 'sec'
        },
      };
    } catch (error) {
      console.error('Error calculating test ranges:', error);
      // 如果出错，返回默认范围
      return {
        deadlift: { min: 70, max: 350, step: 1, unit: 'lbs' },
        pushUp: { min: 5, max: 62, step: 1, unit: 'reps' },
        sprintDragCarry: { min: 89, max: 347, step: 1, unit: 'sec' },
        plank: { min: 41, max: 220, step: 1, unit: 'sec' },
        twoMileRun: { min: 802, max: 1672, step: 1, unit: 'sec' },
      };
    }
  }, [input.age, input.gender]); // 只有当age或gender变化时才重新计算





  // 计算单项得分的辅助函数 - 使用useMemo优化性能
  const getEventScore = useMemo(() => {
    return (eventType: string, value: number): number => {
      try {
        // 使用新的算法计算各项目分数
        switch (eventType) {
          case 'deadlift':
            return calculateMDLScore(value, input.age, input.gender);
          case 'pushUp':
            return calculateHRPScore(value, input.age, input.gender);
          case 'sprintDragCarry':
            return calculateSDCScore(value, input.age, input.gender);
          case 'plank':
            return calculatePLKScore(value, input.age, input.gender);
          case 'twoMileRun':
            return calculate2MRScore(value, input.age, input.gender);
          default:
            return 0;
        }
      } catch {
        return 0;
      }
    };
  }, [input]);

  // 实时计算当前分数 - 不依赖防抖，用于实时显示
  const currentScores = useMemo(() => {
    try {
      const deadliftScore = calculateMDLScore(input.deadlift, input.age, input.gender);
      const pushUpScore = calculateHRPScore(input.pushUp, input.age, input.gender);
      const sprintDragCarryScore = calculateSDCScore(input.sprintDragCarry, input.age, input.gender);
      const plankScore = calculatePLKScore(input.plank, input.age, input.gender);
      const twoMileRunScore = calculate2MRScore(input.twoMileRun, input.age, input.gender);

      const totalScore = deadliftScore + pushUpScore + sprintDragCarryScore + plankScore + twoMileRunScore;

      // 检查是否通过
      const minimumEventScore = 60;
      const minimumTotalScore = 300;
      const allEventsPass = [deadliftScore, pushUpScore, sprintDragCarryScore, plankScore, twoMileRunScore]
        .every(score => score >= minimumEventScore);
      const passed = allEventsPass && totalScore >= minimumTotalScore;

      // 计算各项目的表现等级
      const eventScores = [deadliftScore, pushUpScore, sprintDragCarryScore, plankScore, twoMileRunScore];
      const excellentEvents = eventScores.filter(score => score >= 90).length;
      const veryGoodEvents = eventScores.filter(score => score >= 80 && score < 90).length;
      const goodEvents = eventScores.filter(score => score >= 70 && score < 80).length;
      const satisfactoryEvents = eventScores.filter(score => score >= 60 && score < 70).length;
      const unsatisfactoryEvents = eventScores.filter(score => score < 60).length;

      // 计算平均分数用于百分比显示
      const averageScore = totalScore / 5;

      return {
        deadlift: deadliftScore,
        pushUp: pushUpScore,
        sprintDragCarry: sprintDragCarryScore,
        plank: plankScore,
        twoMileRun: twoMileRunScore,
        total: totalScore,
        average: averageScore,
        passed: passed,
        allEventsPass: allEventsPass,
        excellentEvents,
        veryGoodEvents,
        goodEvents,
        satisfactoryEvents,
        unsatisfactoryEvents
      };
    } catch (error) {
      console.error('Error calculating current scores:', error);
      return {
        deadlift: 0,
        pushUp: 0,
        sprintDragCarry: 0,
        plank: 0,
        twoMileRun: 0,
        total: 0,
        average: 0,
        passed: false,
        allEventsPass: false,
        excellentEvents: 0,
        veryGoodEvents: 0,
        goodEvents: 0,
        satisfactoryEvents: 0,
        unsatisfactoryEvents: 5
      };
    }
  }, [input]);

  // 动态百分比和等级显示
  const performanceInfo = useMemo(() => {
    const { average, excellentEvents, veryGoodEvents, unsatisfactoryEvents } = currentScores;

    // 基于总分的百分比等级（更精确的划分）
    let percentileText = '';
    let performanceLevel = '';
    let levelColor = '';

    if (average >= 95) {
      percentileText = 'Top 5%';
      performanceLevel = 'Outstanding';
      levelColor = 'text-green-600';
    } else if (average >= 90) {
      percentileText = 'Top 10%';
      performanceLevel = 'Excellent';
      levelColor = 'text-green-600';
    } else if (average >= 85) {
      percentileText = 'Top 20%';
      performanceLevel = 'Very Good+';
      levelColor = 'text-blue-600';
    } else if (average >= 80) {
      percentileText = 'Top 30%';
      performanceLevel = 'Very Good';
      levelColor = 'text-blue-600';
    } else if (average >= 75) {
      percentileText = 'Top 40%';
      performanceLevel = 'Good+';
      levelColor = 'text-yellow-600';
    } else if (average >= 70) {
      percentileText = 'Top 50%';
      performanceLevel = 'Good';
      levelColor = 'text-yellow-600';
    } else if (average >= 65) {
      percentileText = 'Above 60%';
      performanceLevel = 'Satisfactory+';
      levelColor = 'text-orange-600';
    } else if (average >= 60) {
      percentileText = 'Above 70%';
      performanceLevel = 'Satisfactory';
      levelColor = 'text-orange-600';
    } else {
      percentileText = 'Below 70%';
      performanceLevel = 'Needs Improvement';
      levelColor = 'text-red-600';
    }

    // 动态鼓励信息
    let encouragementMessage = '';

    if (excellentEvents >= 4) {
      encouragementMessage = 'Outstanding performance! You\'re in elite fitness condition!';
    } else if (excellentEvents >= 2) {
      encouragementMessage = 'Excellent work! You\'re performing at a high level!';
    } else if (currentScores.passed && currentScores.allEventsPass) {
      if (veryGoodEvents >= 3) {
        encouragementMessage = 'Great job! You\'re well above the minimum standards!';
      } else {
        encouragementMessage = 'Good work! You\'ve passed all events successfully!';
      }
    } else if (currentScores.allEventsPass) {
      encouragementMessage = 'You\'re passing all events! Focus on improving your total score.';
    } else if (unsatisfactoryEvents === 1) {
      encouragementMessage = 'Almost there! Focus on improving one more event to pass.';
    } else if (unsatisfactoryEvents <= 2) {
      encouragementMessage = 'You\'re making progress! Keep working on those challenging events.';
    } else {
      encouragementMessage = 'Keep pushing! Every workout gets you closer to your goals!';
    }

    return {
      percentileText,
      performanceLevel,
      levelColor,
      encouragementMessage
    };
  }, [currentScores]);

  const handleInputChange = (field: keyof AFTInput, value: string | number) => {
    setInput(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="max-w-7xl mx-auto p-3 sm:p-5 bg-white">
      {/* 整体容器 - 响应式布局 */}
      <div className="border border-gray-200 bg-white rounded-lg p-3 sm:p-5">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6 min-h-0">
          {/* 左侧区域 - 健身项目详情 */}
          <div className="xl:col-span-2 flex flex-col">
            {/* 顶部筛选栏 - 移动端优化布局 */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-4 p-3 sm:p-0">
              <div className="flex items-center gap-3">
                <label className="text-sm sm:text-base font-bold text-black uppercase tracking-wide">GENDER</label>
                <div className="flex gap-3 sm:gap-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="gender"
                      value="male"
                      checked={input.gender === 'male'}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      className="w-4 h-4 text-[#FFCC01] border-[#FFCC01] focus:ring-[#FFCC01] focus:ring-2"
                    />
                    <span className="text-sm font-medium text-black">M/C</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="gender"
                      value="female"
                      checked={input.gender === 'female'}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      className="w-4 h-4 text-[#FFCC01] border-[#FFCC01] focus:ring-[#FFCC01] focus:ring-2"
                    />
                    <span className="text-sm font-medium text-black">F</span>
                  </label>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <label className="text-sm sm:text-base font-bold text-black uppercase tracking-wide">AGE</label>
                <Select value={getAgeGroupDisplay(input.age)} onValueChange={(value) => handleInputChange('age', getAgeFromGroup(value))}>
                  <SelectTrigger className="h-10 w-20 sm:w-24 text-sm border-[#FFCC01] focus:border-[#FFCC01] focus:ring-[#FFCC01] bg-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="17-21">17 - 21</SelectItem>
                    <SelectItem value="22-26">22 - 26</SelectItem>
                    <SelectItem value="27-31">27 - 31</SelectItem>
                    <SelectItem value="32-36">32 - 36</SelectItem>
                    <SelectItem value="37-41">37 - 41</SelectItem>
                    <SelectItem value="42-46">42 - 46</SelectItem>
                    <SelectItem value="47-51">47 - 51</SelectItem>
                    <SelectItem value="52-56">52 - 56</SelectItem>
                    <SelectItem value="57-61">57 - 61</SelectItem>
                    <SelectItem value="62+">62+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 项目列表区域 */}
            <div className="space-y-4 flex-1">
            {/* MDL - 3 Repetition Maximum Deadlift */}
            <InputSlider
              value={input.deadlift}
              onChange={(value) => handleInputChange('deadlift', value)}
              min={testRanges.deadlift.min}
              max={testRanges.deadlift.max}
              step={testRanges.deadlift.step}
              unit="lbs"
              label="MDL"
              description="3 REPETITION MAXIMUM DEADLIFT"
              icon={<DeadliftIcon className="w-8 h-8" />}
              formatDisplay={(value) => `${value}lbs.`}
              getScore={(value) => getEventScore('deadlift', value)}
            />

            {/* HRP - Hand Release Push-Up */}
            <InputSlider
              value={input.pushUp}
              onChange={(value) => handleInputChange('pushUp', value)}
              min={testRanges.pushUp.min}
              max={testRanges.pushUp.max}
              step={testRanges.pushUp.step}
              unit="reps"
              label="HRP"
              description="HAND RELEASE PUSH-UP - ARM EXTENSION"
              icon={<PushUpIcon className="w-8 h-8" />}
              formatDisplay={(value) => `${value}reps.`}
              getScore={(value) => getEventScore('pushUp', value)}
            />



            {/* SDC - Sprint-Drag-Carry */}
            <InputSlider
              value={input.sprintDragCarry}
              onChange={(value) => handleInputChange('sprintDragCarry', value)}
              min={testRanges.sprintDragCarry.min}
              max={testRanges.sprintDragCarry.max}
              step={testRanges.sprintDragCarry.step}
              unit="sec"
              label="SDC"
              description="SPRINT-DRAG-CARRY"
              icon={<SprintIcon className="w-8 h-8" />}
              formatDisplay={(value) => `${Math.floor(value / 60)}min ${value % 60}sec`}
              getScore={(value) => getEventScore('sprintDragCarry', value)}
              isTimeEvent={true}
            />

            {/* PLK - Plank */}
            <InputSlider
              value={input.plank}
              onChange={(value) => handleInputChange('plank', value)}
              min={testRanges.plank.min}
              max={testRanges.plank.max}
              step={testRanges.plank.step}
              unit="sec"
              label="PLK"
              description="PLANK"
              icon={<PlankIcon className="w-8 h-8" />}
              formatDisplay={(value) => `${Math.floor(value / 60)}min ${value % 60}sec`}
              getScore={(value) => getEventScore('plank', value)}
            />

            {/* 2MR - Two Mile Run */}
            <InputSlider
              value={input.twoMileRun}
              onChange={(value) => handleInputChange('twoMileRun', value)}
              min={testRanges.twoMileRun.min}
              max={testRanges.twoMileRun.max}
              step={testRanges.twoMileRun.step}
              unit="sec"
              label="2MR"
              description="TWO-MILE RUN"
              icon={<RunIcon className="w-8 h-8" />}
              formatDisplay={(value) => `${Math.floor(value / 60)}min ${value % 60}sec`}
              getScore={(value) => getEventScore('twoMileRun', value)}
              isTimeEvent={true}
            />
            </div>
          </div>

          {/* 右侧区域 - 综合评分及雷达图，移动端优化 */}
          <div className="flex flex-col h-full space-y-3 sm:space-y-4">
            {/* 顶部评分信息 - 移动端紧凑布局 */}
            <Card className="border border-gray-200 bg-white rounded-lg">
              <CardContent className="p-3 sm:p-6">
                <div className="text-center space-y-2 sm:space-y-3">
                  {/* 标题行 - 包含品牌标识 */}
                  <div className="flex items-center justify-center gap-2 relative">
                    <div className="text-xs sm:text-sm text-black font-semibold">Overall AFT Score</div>

                    {/* 嵌入式页面的品牌标识 - 与标题同行 */}
                    {isEmbedded && brandStyle !== 'hidden' && (
                      <div className="absolute right-0 top-1/2 -translate-y-1/2">
                        {brandStyle === 'minimal' && (
                          <a
                            href="https://aftcalculator.online"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[8px] text-gray-400 hover:text-gray-600 transition-colors opacity-50 hover:opacity-70"
                            title="Powered by AFT Calculator"
                          >
                            aftcalculator.online
                          </a>
                        )}

                        {brandStyle === 'badge' && (
                          <a
                            href="https://aftcalculator.online"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-[#FFCC01]/10 hover:to-[#FFCC01]/5 rounded border border-gray-200 hover:border-[#FFCC01]/30 transition-all duration-300"
                            title="Powered by AFT Calculator"
                          >
                            <svg className="w-2 h-2 text-[#FFCC01]" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                            </svg>
                            <span className="text-[8px] font-medium text-gray-600 hover:text-gray-800">
                              AFT
                            </span>
                          </a>
                        )}

                        {brandStyle === 'subtle' && (
                          <div className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-white/50 rounded-full border border-gray-100">
                            <div className="w-1 h-1 bg-[#FFCC01] rounded-full opacity-60"></div>
                            <a
                              href="https://aftcalculator.online"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[8px] text-gray-500 hover:text-gray-700 transition-colors font-light"
                              title="Powered by AFT Calculator"
                            >
                              AFT
                            </a>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="text-3xl sm:text-5xl font-bold text-black">
                    {currentScores.total}
                  </div>

                  {/* 动态百分比显示 */}
                  <div className="text-xs sm:text-sm text-[#FFCC01] font-semibold">
                    {performanceInfo.percentileText}
                  </div>

                  {/* 通过状态 - 移动端适中字体 */}
                  <div className={`text-2xl sm:text-5xl font-bold ${currentScores.passed ? 'text-green-500' : 'text-red-500'}`}>
                    {currentScores.passed ? 'Passed!' : 'Failed'}
                  </div>

                  {/* 动态鼓励信息 - 移动端更紧凑 */}
                  <p className="text-xs sm:text-sm text-gray-600 leading-relaxed px-2 sm:px-0">
                    {performanceInfo.encouragementMessage}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 雷达图区域 - 移动端优化尺寸 */}
            <Card className="border border-gray-200 bg-white rounded-lg flex-1 min-h-0">
              <CardContent className="p-1 sm:p-2 h-full flex items-center justify-center relative">
                {isClient ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <RadarChart
                      data={{
                        MDL: currentScores.deadlift,
                        HRP: currentScores.pushUp,
                        '2MR': currentScores.twoMileRun,
                        PLK: currentScores.plank,
                        SDC: currentScores.sprintDragCarry,
                      }}
                      size={Math.min(280, typeof window !== 'undefined' && window.innerWidth > 640 ? 280 : 240)}
                      className="w-full h-full"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400 text-sm">
                    Loading chart...
                  </div>
                )}
                

              </CardContent>
            </Card>
          </div>
        </div>

      </div>
    </div>
  );
}
