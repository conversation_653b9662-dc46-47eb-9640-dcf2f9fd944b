'use client';

import dynamic from 'next/dynamic';

// 动态导入AFTCalculator以避免SSR问题
const AFTCalculator = dynamic(
  () => import('./AFTCalculator'),
  {
    loading: () => (
      <div className="text-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FFCC01] mx-auto mb-4"></div>
        <p className="text-gray-600">Loading AFT Calculator...</p>
      </div>
    ),
    ssr: false
  }
);

export default function AFTCalculatorWrapper() {
  return <AFTCalculator />;
}
