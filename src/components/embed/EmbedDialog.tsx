'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Copy, Check, Code } from 'lucide-react';
import { siteConfig } from '@/config/site';

interface EmbedDialogProps {
  children: React.ReactNode;
}

export default function EmbedDialog({ children }: EmbedDialogProps) {
  const [copied, setCopied] = useState(false);
  const [open, setOpen] = useState(false);
  const [embedType, setEmbedType] = useState<'calculator' | 'standards'>('calculator');

  // Get current domain and path
  const getEmbedCode = () => {
    // Use configured site URL for consistent embed code generation
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : siteConfig.url;
    const path = embedType === 'standards' ? '/embed-aft-standards' : '/embed-aft-calculator';
    const title = embedType === 'standards' ? 'AFT Standards Calculator' : 'AFT Calculator';
    return `<iframe src="${baseUrl}${path}" style="width: 100%; height: 800px; border: none; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);" title="${title}" frameborder="0" allowfullscreen></iframe>`;
  };

  const embedCode = getEmbedCode();

  const copyToClipboard = async () => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // Use modern Clipboard API
        await navigator.clipboard.writeText(embedCode);
      } else {
        // Fallback: use traditional method
        const textArea = document.createElement('textarea');
        textArea.value = embedCode;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }
      
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Copy failed:', err);
      // Error notification can be added here
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg md:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Code className="h-5 w-5 text-[#FFCC01]" />
            Embed Code
          </DialogTitle>
          <DialogDescription>
            Copy the code below and paste it into your website to embed the AFT Calculator
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Calculator Type Selection */}
          <div className="space-y-3">
            <Label>Calculator Type</Label>
            <RadioGroup
              value={embedType}
              onValueChange={(value) => setEmbedType(value as 'calculator' | 'standards')}
              className="flex flex-row space-x-6"
            >
              <label className="flex items-center space-x-2 cursor-pointer">
                <RadioGroupItem value="calculator" id="calculator" />
                <span className="text-sm font-medium leading-none">AFT Score Calculator</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <RadioGroupItem value="standards" id="standards" />
                <span className="text-sm font-medium leading-none">AFT Standards Calculator</span>
              </label>
            </RadioGroup>
          </div>

          {/* Code input field */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              iframe Embed Code:
            </label>
            <div className="flex gap-2">
              <Input
                value={embedCode}
                readOnly
                className="font-mono text-xs bg-gray-50 border-gray-300"
                onClick={(e) => (e.target as HTMLInputElement).select()}
              />
              <Button
                onClick={copyToClipboard}
                variant="outline"
                size="sm"
                className="flex-shrink-0"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 text-green-600" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4" />
                    Copy
                  </>
                )}
              </Button>
            </div>
          </div>

        </div>
      </DialogContent>
    </Dialog>
  );
}
