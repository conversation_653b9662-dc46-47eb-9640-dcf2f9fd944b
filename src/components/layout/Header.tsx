'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { Button } from '@/components/ui/button';
import { Target, Menu, Code } from 'lucide-react';
import { cn } from '@/lib/utils';
import EmbedDialog from '@/components/embed/EmbedDialog';

const navigation = [
  {
    name: 'Army Score Chart',
    href: '/aft-score-chart',
    icon: Target,
  },
  {
    name: 'AFT Standards',
    href: '/aft-standards',
    icon: Target,
  },
  // {
  //   name: 'Download App',
  //   href: '/download',
  //   icon: Smartphone,
  // },
];

export default function Header() {
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 shadow-sm">
      <div className="container mx-auto px-3 sm:px-4">
        <div className="flex h-14 sm:h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <Image
              src="/logo.png"
              alt="AFT Calculator Logo"
              width={40}
              height={40}
              className="h-8 w-8 sm:h-10 sm:w-10"
              priority
              quality={90}
            />
            <span className="text-lg sm:text-xl font-bold text-black">AFT Calculator Online</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <nav className="flex items-center space-x-6">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-1 text-sm font-medium transition-colors hover:text-[#FFCC01] px-3 py-2 rounded-md",
                      pathname === item.href
                        ? "text-[#FFCC01] bg-[#FFCC01]/10"
                        : "text-gray-600 hover:bg-[#FFCC01]/5"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </nav>

            {/* Embed Code Button */}
            <EmbedDialog>
              <Button variant="outline" size="sm" className="flex items-center gap-2 border-[#FFCC01] text-[#FFCC01] hover:bg-[#FFCC01]/10">
                <Code className="h-4 w-4" />
                Embed Code
              </Button>
            </EmbedDialog>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="h-10 w-10 p-0 border-2 border-[#FFCC01] hover:bg-[#FFCC01]/10 bg-white rounded-md touch-manipulation">
                    <Menu className="h-5 w-5 text-[#FFCC01]" />
                    <span className="sr-only">Toggle menu</span>
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[280px] sm:w-[300px] gap-2 sm:gap-3 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
                      {navigation.map((item) => {
                        const Icon = item.icon;
                        return (
                          <NavigationMenuLink key={item.name} asChild>
                            <Link
                              href={item.href}
                              className={cn(
                                "flex items-center space-x-2 rounded-md p-2 sm:p-3 text-sm font-medium transition-colors hover:bg-[#FFCC01]/10 hover:text-[#FFCC01] touch-manipulation",
                                pathname === item.href
                                  ? "bg-[#FFCC01]/10 text-[#FFCC01]"
                                  : "text-gray-600"
                              )}
                            >
                              <Icon className="h-4 w-4" />
                              <span>{item.name}</span>
                            </Link>
                          </NavigationMenuLink>
                        );
                      })}

                      {/* Embed Code Button for Mobile */}
                      <div className="border-t border-gray-200 pt-2 sm:pt-3 mt-2 sm:mt-3">
                        <EmbedDialog>
                          <button className="flex items-center space-x-2 rounded-md p-2 sm:p-3 text-sm font-medium transition-colors hover:bg-[#FFCC01]/10 hover:text-[#FFCC01] touch-manipulation text-gray-600 w-full text-left">
                            <Code className="h-4 w-4" />
                            <span>Embed Code</span>
                          </button>
                        </EmbedDialog>
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
