import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const footerLinks = {
  calculator: [
    { name: 'AFT Calculator', href: '/' },
    { name: 'Score Standards', href: '/standards' },
    { name: 'Training Guide', href: '/training' },
  ],
  resources: [
    { name: 'FAQ', href: '/faq' },
    // { name: 'Download App', href: '/download' },
    { name: 'About', href: '/about' },
  ],
};

export default function Footer() {
  return (
    <footer className="border-t border-gray-200 bg-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Image
                src="/logo.png"
                alt="AFT Calculator Logo"
                width={40}
                height={40}
                className="h-10 w-10"
              />
              <span className="text-xl font-bold text-black">AFT Calculator Online</span>
            </div>
            <p className="text-sm text-gray-600">
              Professional Army Fitness Test calculator and training resources.
              Calculate your AFT score and improve your military fitness.
            </p>
          </div>

          {/* Calculator Links */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-black">Calculator</h3>
            <ul className="space-y-2">
              {footerLinks.calculator.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-[#FFCC01] transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-black">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-[#FFCC01] transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
              <div className="text-sm text-gray-600">
                © {new Date().getFullYear()} AFT Calculator. All rights reserved.
              </div>
              {/* <div className="text-sm text-gray-600">
                Support: <a href="mailto:<EMAIL>" className="text-[#FFCC01] hover:underline"><EMAIL></a>
              </div> */}
            </div>
            <div className="flex space-x-6">
              <Link
                href="/privacy"
                className="text-sm text-gray-600 hover:text-[#FFCC01] transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-sm text-gray-600 hover:text-[#FFCC01] transition-colors"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
