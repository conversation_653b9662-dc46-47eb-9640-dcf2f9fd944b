'use client';

import React from 'react';

interface RadarChartProps {
  data: {
    MDL: number;
    HRP: number;
    SDC: number;
    PLK: number;
    '2MR': number;
  };
  className?: string;
  size?: number;
}

export const RadarChart: React.FC<RadarChartProps> = ({ data, className = "", size = 240 }) => {
  const center = size / 2;
  const radius = Math.min(size * 0.35, 120); // 动态计算半径，最大120
  
  // 定义五个顶点的角度（从顶部开始，顺时针）
  const angles = [
    -Math.PI / 2,                    // MDL (top)
    -Math.PI / 2 + (2 * Math.PI) / 5,  // HRP (top right)
    -Math.PI / 2 + (4 * Math.PI) / 5,  // 2MR (bottom right)
    -Math.PI / 2 + (6 * Math.PI) / 5,  // PLK (bottom left)
    -Math.PI / 2 + (8 * Math.PI) / 5   // SDC (top left)
  ];

  const labels = ['MDL', 'HRP', '2MR', 'PLK', 'SDC'];
  
  // 计算顶点坐标
  const getPoint = (angle: number, distance: number) => ({
    x: center + Math.cos(angle) * distance,
    y: center + Math.sin(angle) * distance
  });
  
  // 生成网格线
  const gridLevels = [20, 40, 60, 80, 100];
  
  // 生成数据点
  const dataPoints = angles.map((angle, index) => {
    const value = Object.values(data)[index];
    const normalizedValue = Math.min(value, 100) / 100; // 归一化到0-1
    return getPoint(angle, radius * normalizedValue);
  });
  
  // 生成数据区域路径
  const dataPath = dataPoints.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ') + ' Z';
  
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="overflow-visible max-w-full max-h-full"
        viewBox={`0 0 ${size} ${size}`}
        preserveAspectRatio="xMidYMid meet"
      >
        {/* 网格线 */}
        {gridLevels.map((level) => {
          const gridRadius = (radius * level) / 100;
          const gridPoints = angles.map(angle => getPoint(angle, gridRadius));
          const gridPath = gridPoints.map((point, index) => 
            `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
          ).join(' ') + ' Z';
          
          return (
            <path
              key={level}
              d={gridPath}
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="1"
            />
          );
        })}
        
        {/* 轴线 */}
        {angles.map((angle, index) => {
          const endPoint = getPoint(angle, radius);
          return (
            <line
              key={index}
              x1={center}
              y1={center}
              x2={endPoint.x}
              y2={endPoint.y}
              stroke="#e5e7eb"
              strokeWidth="1"
            />
          );
        })}
        
        {/* 数据区域 */}
        <path
          d={dataPath}
          fill="#FFCC01"
          fillOpacity="0.3"
          stroke="#FFCC01"
          strokeWidth="2"
        />
        
        {/* 数据点 */}
        {dataPoints.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="5"
            fill="#FFCC01"
            stroke="white"
            strokeWidth="2"
          />
        ))}
        
        {/* 标签 */}
        {angles.map((angle, index) => {
          const labelDistance = radius + Math.max(20, size * 0.08); // 动态标签距离
          const labelPoint = getPoint(angle, labelDistance);
          return (
            <text
              key={index}
              x={labelPoint.x}
              y={labelPoint.y}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-base font-semibold fill-gray-700"
            >
              {labels[index]}
            </text>
          );
        })}
        
        {/* 数值标签 */}
        {dataPoints.map((point, index) => {
          const value = Object.values(data)[index];
          return (
            <text
              key={`value-${index}`}
              x={point.x}
              y={point.y - 10}
              textAnchor="middle"
              dominantBaseline="middle"
              className="text-sm font-bold fill-gray-800"
            >
              {value}
            </text>
          );
        })}
      </svg>
    </div>
  );
};
