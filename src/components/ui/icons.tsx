import React from 'react';
import Image from 'next/image';

interface IconProps {
  className?: string;
  size?: number;
}

export const DeadliftIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/deadlift.svg"
    alt="Deadlift"
    width={size}
    height={size}
    className={className}
  />
);

export const PushUpIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/pushup.svg"
    alt="Push Up"
    width={size}
    height={size}
    className={className}
  />
);

export const SprintIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/dragcarry.svg"
    alt="Sprint Drag Carry"
    width={size}
    height={size}
    className={className}
  />
);

export const LegTuckIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/plank.svg"
    alt="Leg Tuck"
    width={size}
    height={size}
    className={className}
  />
);

export const PlankIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/plank.svg"
    alt="Plank"
    width={size}
    height={size}
    className={className}
  />
);

export const RunIcon: React.FC<IconProps> = ({ className = "", size = 24 }) => (
  <Image
    src="/run.svg"
    alt="Two Mile Run"
    width={size}
    height={size}
    className={className}
  />
);
