#!/usr/bin/env node

/**
 * 测试环境变量配置脚本
 * 验证 Google AdSense 广告单元 ID 是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查 Google AdSense 环境变量配置...\n');

// 手动读取 .env.local 文件
function loadEnvFile(filePath) {
  try {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    return envVars;
  } catch (error) {
    console.log(`⚠️  无法读取 ${filePath}: ${error.message}`);
    return {};
  }
}

const envVars = loadEnvFile('.env.local');

const requiredEnvVars = {
  'NEXT_PUBLIC_ADSENSE_ACCOUNT': envVars.NEXT_PUBLIC_ADSENSE_ACCOUNT,
  'NEXT_PUBLIC_ADSENSE_PUBLISHER': envVars.NEXT_PUBLIC_ADSENSE_PUBLISHER,
  'NEXT_PUBLIC_AD_UNIT_LEADERBOARD': envVars.NEXT_PUBLIC_AD_UNIT_LEADERBOARD,
  'NEXT_PUBLIC_AD_UNIT_LARGE_RECTANGLE': envVars.NEXT_PUBLIC_AD_UNIT_LARGE_RECTANGLE,
  'NEXT_PUBLIC_AD_UNIT_MEDIUM_RECTANGLE': envVars.NEXT_PUBLIC_AD_UNIT_MEDIUM_RECTANGLE,
  'NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER': envVars.NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER,
};

const expectedValues = {
  'NEXT_PUBLIC_ADSENSE_ACCOUNT': 'ca-pub-****************',
  'NEXT_PUBLIC_ADSENSE_PUBLISHER': 'pub-****************',
  'NEXT_PUBLIC_AD_UNIT_LEADERBOARD': '**********',
  'NEXT_PUBLIC_AD_UNIT_LARGE_RECTANGLE': '**********',
  'NEXT_PUBLIC_AD_UNIT_MEDIUM_RECTANGLE': '**********',
  'NEXT_PUBLIC_AD_UNIT_MOBILE_BANNER': '**********',
};

let allValid = true;

console.log('📋 环境变量检查结果:\n');

Object.entries(requiredEnvVars).forEach(([key, value]) => {
  const expected = expectedValues[key];
  const isValid = value === expected;
  const status = isValid ? '✅' : '❌';
  
  console.log(`${status} ${key}`);
  console.log(`   当前值: ${value || '未设置'}`);
  console.log(`   期望值: ${expected}`);
  
  if (!isValid) {
    allValid = false;
  }
  console.log('');
});

console.log('📊 总结:');
if (allValid) {
  console.log('✅ 所有环境变量配置正确！');
  console.log('🚀 Google AdSense 广告单元已准备就绪');
} else {
  console.log('❌ 发现配置问题，请检查 .env.local 文件');
  console.log('💡 确保所有广告单元 ID 都已正确设置');
}

console.log('\n🔗 广告单元映射:');
console.log('   Leaderboard (728x90): **********');
console.log('   Large Rectangle (336x280): **********');
console.log('   Medium Rectangle (300x250): **********');
console.log('   Mobile Banner (300x50): **********');

process.exit(allValid ? 0 : 1);
